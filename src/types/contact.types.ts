import type { RouterInputs, RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type ContactCreateInput = RouterInputs["contacts"]["create"];

export type ContactUpdateInput = RouterInputs["contacts"]["updateById"];

export type ContactsFindInput = RouterInputs["contacts"]["getAll"];

export type Contact = RouterOutputs["contacts"]["getById"];

export type ContactsOutput = RouterOutputs["contacts"]["getAll"];

export type InfiniteContactsData = InfiniteData<ContactsOutput>;

export type ContactType = "lead" | "client" | "other";
