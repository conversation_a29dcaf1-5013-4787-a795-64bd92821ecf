import type { RouterInputs, RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type ProjectCreateInput = RouterInputs["projects"]["create"];

export type ProjectUpdateInput = RouterInputs["projects"]["updateById"];

export type ProjectsFindInput = RouterInputs["projects"]["getAll"];

export type ProjectsByContactIdFindInput =
  RouterInputs["projects"]["getProjectsByContactId"];

export type Project = RouterOutputs["projects"]["getById"];

export type ProjectsOutput = RouterOutputs["projects"]["getAll"];

export type InfiniteProjectsData = InfiniteData<ProjectsOutput>;

export type ProjectStatus = "planned" | "in progress" | "completed" | "paused";
