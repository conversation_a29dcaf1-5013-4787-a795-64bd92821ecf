import { type z } from "@/lib/zod";
import { type invoiceItemSchema } from "@/schemas/invoice.schemas";
import type { RouterInputs, RouterOutputs } from "@/trpc/react";
import type { InfiniteData } from "@tanstack/react-query";

export type InvoiceCreateInput = RouterInputs["invoices"]["create"];

export type InvoiceUpdateInput = RouterInputs["invoices"]["updateById"];

export type InvoicesFindInput = RouterInputs["invoices"]["getAll"];

export type Invoice = RouterOutputs["invoices"]["getById"];

export type InvoicePublic = RouterOutputs["invoices"]["getByIdPublic"];

export type InvoicesOutput = RouterOutputs["invoices"]["getAll"];

export type InfiniteInvoicesData = InfiniteData<InvoicesOutput>;

export type InvoiceType = "one_time" | "recurring";

export type InvoiceStatus = "draft" | "sent" | "paid" | "read" | "overdue";

export type InvoiceItem = z.infer<typeof invoiceItemSchema>;
