import ContactPageLayout from "@/components/contacts/contact-page-layout";
import { type Metadata } from "next";

interface Props {
  children: React.ReactNode;
  params: { contactId: string };
}

export const metadata: Metadata = {
  title: "Contacts",
};

export default function Layout({ children, params: { contactId } }: Props) {
  return (
    <ContactPageLayout contactId={contactId}>{children}</ContactPageLayout>
  );
}
