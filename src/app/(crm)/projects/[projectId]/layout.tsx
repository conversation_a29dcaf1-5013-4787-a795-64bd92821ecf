import { ProjectPageLayout } from "@/components/projects/project-page-layout";
import { type Metadata } from "next";

interface Props {
  children: React.ReactNode;
  params: Promise<{ projectId: string }>;
}

export const metadata: Metadata = {
  title: "Projects",
};

export default async function Layout({ children, params }: Props) {
  const { projectId } = await params;
  return (
    <ProjectPageLayout projectId={projectId}>{children}</ProjectPageLayout>
  );
}
