
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Invoice` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Invoice
 * 
 */
export type InvoiceModel = runtime.Types.Result.DefaultSelection<Prisma.$InvoicePayload>

export type AggregateInvoice = {
  _count: InvoiceCountAggregateOutputType | null
  _avg: InvoiceAvgAggregateOutputType | null
  _sum: InvoiceSumAggregateOutputType | null
  _min: InvoiceMinAggregateOutputType | null
  _max: InvoiceMaxAggregateOutputType | null
}

export type InvoiceAvgAggregateOutputType = {
  amountDue: number | null
  amountPaid: number | null
  discount: number | null
  fee: number | null
  net: number | null
  subtotal: number | null
  tax: number | null
  total: number | null
}

export type InvoiceSumAggregateOutputType = {
  amountDue: number | null
  amountPaid: number | null
  discount: number | null
  fee: number | null
  net: number | null
  subtotal: number | null
  tax: number | null
  total: number | null
}

export type InvoiceMinAggregateOutputType = {
  id: string | null
  amountDue: number | null
  amountPaid: number | null
  discount: number | null
  dueOn: string | null
  fromAddress: string | null
  fromCompany: string | null
  fromName: string | null
  fromEmail: string | null
  invoiceNumber: string | null
  issuedOn: string | null
  unitsType: string | null
  notes: string | null
  paymentDate: string | null
  logoImage: string | null
  fee: number | null
  net: number | null
  paymentMethod: string | null
  transactionId: string | null
  isStripeEnabled: boolean | null
  isPaypalEnabled: boolean | null
  isCashappEnabled: boolean | null
  isZelleEnabled: boolean | null
  sentDate: string | null
  status: string | null
  subtotal: number | null
  tax: number | null
  taxLabel: string | null
  toAddress: string | null
  toCompany: string | null
  toEmail: string | null
  toName: string | null
  total: number | null
  type: string | null
  archived: boolean | null
  userId: string | null
  contactId: string | null
  projectId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type InvoiceMaxAggregateOutputType = {
  id: string | null
  amountDue: number | null
  amountPaid: number | null
  discount: number | null
  dueOn: string | null
  fromAddress: string | null
  fromCompany: string | null
  fromName: string | null
  fromEmail: string | null
  invoiceNumber: string | null
  issuedOn: string | null
  unitsType: string | null
  notes: string | null
  paymentDate: string | null
  logoImage: string | null
  fee: number | null
  net: number | null
  paymentMethod: string | null
  transactionId: string | null
  isStripeEnabled: boolean | null
  isPaypalEnabled: boolean | null
  isCashappEnabled: boolean | null
  isZelleEnabled: boolean | null
  sentDate: string | null
  status: string | null
  subtotal: number | null
  tax: number | null
  taxLabel: string | null
  toAddress: string | null
  toCompany: string | null
  toEmail: string | null
  toName: string | null
  total: number | null
  type: string | null
  archived: boolean | null
  userId: string | null
  contactId: string | null
  projectId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type InvoiceCountAggregateOutputType = {
  id: number
  amountDue: number
  amountPaid: number
  discount: number
  dueOn: number
  fromAddress: number
  fromCompany: number
  fromName: number
  fromEmail: number
  invoiceNumber: number
  issuedOn: number
  items: number
  unitsType: number
  notes: number
  paymentDate: number
  logoImage: number
  fee: number
  net: number
  paymentMethod: number
  transactionId: number
  isStripeEnabled: number
  isPaypalEnabled: number
  isCashappEnabled: number
  isZelleEnabled: number
  sentDate: number
  status: number
  subtotal: number
  tax: number
  taxLabel: number
  toAddress: number
  toCompany: number
  toEmail: number
  toName: number
  total: number
  type: number
  archived: number
  userId: number
  contactId: number
  projectId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type InvoiceAvgAggregateInputType = {
  amountDue?: true
  amountPaid?: true
  discount?: true
  fee?: true
  net?: true
  subtotal?: true
  tax?: true
  total?: true
}

export type InvoiceSumAggregateInputType = {
  amountDue?: true
  amountPaid?: true
  discount?: true
  fee?: true
  net?: true
  subtotal?: true
  tax?: true
  total?: true
}

export type InvoiceMinAggregateInputType = {
  id?: true
  amountDue?: true
  amountPaid?: true
  discount?: true
  dueOn?: true
  fromAddress?: true
  fromCompany?: true
  fromName?: true
  fromEmail?: true
  invoiceNumber?: true
  issuedOn?: true
  unitsType?: true
  notes?: true
  paymentDate?: true
  logoImage?: true
  fee?: true
  net?: true
  paymentMethod?: true
  transactionId?: true
  isStripeEnabled?: true
  isPaypalEnabled?: true
  isCashappEnabled?: true
  isZelleEnabled?: true
  sentDate?: true
  status?: true
  subtotal?: true
  tax?: true
  taxLabel?: true
  toAddress?: true
  toCompany?: true
  toEmail?: true
  toName?: true
  total?: true
  type?: true
  archived?: true
  userId?: true
  contactId?: true
  projectId?: true
  createdAt?: true
  updatedAt?: true
}

export type InvoiceMaxAggregateInputType = {
  id?: true
  amountDue?: true
  amountPaid?: true
  discount?: true
  dueOn?: true
  fromAddress?: true
  fromCompany?: true
  fromName?: true
  fromEmail?: true
  invoiceNumber?: true
  issuedOn?: true
  unitsType?: true
  notes?: true
  paymentDate?: true
  logoImage?: true
  fee?: true
  net?: true
  paymentMethod?: true
  transactionId?: true
  isStripeEnabled?: true
  isPaypalEnabled?: true
  isCashappEnabled?: true
  isZelleEnabled?: true
  sentDate?: true
  status?: true
  subtotal?: true
  tax?: true
  taxLabel?: true
  toAddress?: true
  toCompany?: true
  toEmail?: true
  toName?: true
  total?: true
  type?: true
  archived?: true
  userId?: true
  contactId?: true
  projectId?: true
  createdAt?: true
  updatedAt?: true
}

export type InvoiceCountAggregateInputType = {
  id?: true
  amountDue?: true
  amountPaid?: true
  discount?: true
  dueOn?: true
  fromAddress?: true
  fromCompany?: true
  fromName?: true
  fromEmail?: true
  invoiceNumber?: true
  issuedOn?: true
  items?: true
  unitsType?: true
  notes?: true
  paymentDate?: true
  logoImage?: true
  fee?: true
  net?: true
  paymentMethod?: true
  transactionId?: true
  isStripeEnabled?: true
  isPaypalEnabled?: true
  isCashappEnabled?: true
  isZelleEnabled?: true
  sentDate?: true
  status?: true
  subtotal?: true
  tax?: true
  taxLabel?: true
  toAddress?: true
  toCompany?: true
  toEmail?: true
  toName?: true
  total?: true
  type?: true
  archived?: true
  userId?: true
  contactId?: true
  projectId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type InvoiceAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Invoice to aggregate.
   */
  where?: Prisma.InvoiceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Invoices to fetch.
   */
  orderBy?: Prisma.InvoiceOrderByWithRelationInput | Prisma.InvoiceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.InvoiceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Invoices from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Invoices.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Invoices
  **/
  _count?: true | InvoiceCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: InvoiceAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: InvoiceSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: InvoiceMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: InvoiceMaxAggregateInputType
}

export type GetInvoiceAggregateType<T extends InvoiceAggregateArgs> = {
      [P in keyof T & keyof AggregateInvoice]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateInvoice[P]>
    : Prisma.GetScalarType<T[P], AggregateInvoice[P]>
}




export type InvoiceGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.InvoiceWhereInput
  orderBy?: Prisma.InvoiceOrderByWithAggregationInput | Prisma.InvoiceOrderByWithAggregationInput[]
  by: Prisma.InvoiceScalarFieldEnum[] | Prisma.InvoiceScalarFieldEnum
  having?: Prisma.InvoiceScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: InvoiceCountAggregateInputType | true
  _avg?: InvoiceAvgAggregateInputType
  _sum?: InvoiceSumAggregateInputType
  _min?: InvoiceMinAggregateInputType
  _max?: InvoiceMaxAggregateInputType
}

export type InvoiceGroupByOutputType = {
  id: string
  amountDue: number
  amountPaid: number
  discount: number
  dueOn: string
  fromAddress: string
  fromCompany: string
  fromName: string
  fromEmail: string
  invoiceNumber: string
  issuedOn: string
  items: runtime.JsonValue
  unitsType: string
  notes: string
  paymentDate: string
  logoImage: string | null
  fee: number
  net: number
  paymentMethod: string | null
  transactionId: string | null
  isStripeEnabled: boolean
  isPaypalEnabled: boolean
  isCashappEnabled: boolean
  isZelleEnabled: boolean
  sentDate: string
  status: string
  subtotal: number
  tax: number
  taxLabel: string
  toAddress: string
  toCompany: string
  toEmail: string
  toName: string
  total: number
  type: string
  archived: boolean
  userId: string
  contactId: string | null
  projectId: string | null
  createdAt: Date
  updatedAt: Date
  _count: InvoiceCountAggregateOutputType | null
  _avg: InvoiceAvgAggregateOutputType | null
  _sum: InvoiceSumAggregateOutputType | null
  _min: InvoiceMinAggregateOutputType | null
  _max: InvoiceMaxAggregateOutputType | null
}

type GetInvoiceGroupByPayload<T extends InvoiceGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<InvoiceGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof InvoiceGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], InvoiceGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], InvoiceGroupByOutputType[P]>
      }
    >
  > 



export type InvoiceWhereInput = {
  AND?: Prisma.InvoiceWhereInput | Prisma.InvoiceWhereInput[]
  OR?: Prisma.InvoiceWhereInput[]
  NOT?: Prisma.InvoiceWhereInput | Prisma.InvoiceWhereInput[]
  id?: Prisma.StringFilter<"Invoice"> | string
  amountDue?: Prisma.IntFilter<"Invoice"> | number
  amountPaid?: Prisma.IntFilter<"Invoice"> | number
  discount?: Prisma.FloatFilter<"Invoice"> | number
  dueOn?: Prisma.StringFilter<"Invoice"> | string
  fromAddress?: Prisma.StringFilter<"Invoice"> | string
  fromCompany?: Prisma.StringFilter<"Invoice"> | string
  fromName?: Prisma.StringFilter<"Invoice"> | string
  fromEmail?: Prisma.StringFilter<"Invoice"> | string
  invoiceNumber?: Prisma.StringFilter<"Invoice"> | string
  issuedOn?: Prisma.StringFilter<"Invoice"> | string
  items?: Prisma.JsonFilter<"Invoice">
  unitsType?: Prisma.StringFilter<"Invoice"> | string
  notes?: Prisma.StringFilter<"Invoice"> | string
  paymentDate?: Prisma.StringFilter<"Invoice"> | string
  logoImage?: Prisma.StringNullableFilter<"Invoice"> | string | null
  fee?: Prisma.IntFilter<"Invoice"> | number
  net?: Prisma.IntFilter<"Invoice"> | number
  paymentMethod?: Prisma.StringNullableFilter<"Invoice"> | string | null
  transactionId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  isStripeEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isPaypalEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isCashappEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isZelleEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  sentDate?: Prisma.StringFilter<"Invoice"> | string
  status?: Prisma.StringFilter<"Invoice"> | string
  subtotal?: Prisma.FloatFilter<"Invoice"> | number
  tax?: Prisma.FloatFilter<"Invoice"> | number
  taxLabel?: Prisma.StringFilter<"Invoice"> | string
  toAddress?: Prisma.StringFilter<"Invoice"> | string
  toCompany?: Prisma.StringFilter<"Invoice"> | string
  toEmail?: Prisma.StringFilter<"Invoice"> | string
  toName?: Prisma.StringFilter<"Invoice"> | string
  total?: Prisma.IntFilter<"Invoice"> | number
  type?: Prisma.StringFilter<"Invoice"> | string
  archived?: Prisma.BoolFilter<"Invoice"> | boolean
  userId?: Prisma.StringFilter<"Invoice"> | string
  contactId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  projectId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Invoice"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Invoice"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  contact?: Prisma.XOR<Prisma.ContactNullableScalarRelationFilter, Prisma.ContactWhereInput> | null
  project?: Prisma.XOR<Prisma.ProjectNullableScalarRelationFilter, Prisma.ProjectWhereInput> | null
}

export type InvoiceOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  amountDue?: Prisma.SortOrder
  amountPaid?: Prisma.SortOrder
  discount?: Prisma.SortOrder
  dueOn?: Prisma.SortOrder
  fromAddress?: Prisma.SortOrder
  fromCompany?: Prisma.SortOrder
  fromName?: Prisma.SortOrder
  fromEmail?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  issuedOn?: Prisma.SortOrder
  items?: Prisma.SortOrder
  unitsType?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  paymentDate?: Prisma.SortOrder
  logoImage?: Prisma.SortOrderInput | Prisma.SortOrder
  fee?: Prisma.SortOrder
  net?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrderInput | Prisma.SortOrder
  transactionId?: Prisma.SortOrderInput | Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  sentDate?: Prisma.SortOrder
  status?: Prisma.SortOrder
  subtotal?: Prisma.SortOrder
  tax?: Prisma.SortOrder
  taxLabel?: Prisma.SortOrder
  toAddress?: Prisma.SortOrder
  toCompany?: Prisma.SortOrder
  toEmail?: Prisma.SortOrder
  toName?: Prisma.SortOrder
  total?: Prisma.SortOrder
  type?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  contactId?: Prisma.SortOrderInput | Prisma.SortOrder
  projectId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
  contact?: Prisma.ContactOrderByWithRelationInput
  project?: Prisma.ProjectOrderByWithRelationInput
}

export type InvoiceWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.InvoiceWhereInput | Prisma.InvoiceWhereInput[]
  OR?: Prisma.InvoiceWhereInput[]
  NOT?: Prisma.InvoiceWhereInput | Prisma.InvoiceWhereInput[]
  amountDue?: Prisma.IntFilter<"Invoice"> | number
  amountPaid?: Prisma.IntFilter<"Invoice"> | number
  discount?: Prisma.FloatFilter<"Invoice"> | number
  dueOn?: Prisma.StringFilter<"Invoice"> | string
  fromAddress?: Prisma.StringFilter<"Invoice"> | string
  fromCompany?: Prisma.StringFilter<"Invoice"> | string
  fromName?: Prisma.StringFilter<"Invoice"> | string
  fromEmail?: Prisma.StringFilter<"Invoice"> | string
  invoiceNumber?: Prisma.StringFilter<"Invoice"> | string
  issuedOn?: Prisma.StringFilter<"Invoice"> | string
  items?: Prisma.JsonFilter<"Invoice">
  unitsType?: Prisma.StringFilter<"Invoice"> | string
  notes?: Prisma.StringFilter<"Invoice"> | string
  paymentDate?: Prisma.StringFilter<"Invoice"> | string
  logoImage?: Prisma.StringNullableFilter<"Invoice"> | string | null
  fee?: Prisma.IntFilter<"Invoice"> | number
  net?: Prisma.IntFilter<"Invoice"> | number
  paymentMethod?: Prisma.StringNullableFilter<"Invoice"> | string | null
  transactionId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  isStripeEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isPaypalEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isCashappEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isZelleEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  sentDate?: Prisma.StringFilter<"Invoice"> | string
  status?: Prisma.StringFilter<"Invoice"> | string
  subtotal?: Prisma.FloatFilter<"Invoice"> | number
  tax?: Prisma.FloatFilter<"Invoice"> | number
  taxLabel?: Prisma.StringFilter<"Invoice"> | string
  toAddress?: Prisma.StringFilter<"Invoice"> | string
  toCompany?: Prisma.StringFilter<"Invoice"> | string
  toEmail?: Prisma.StringFilter<"Invoice"> | string
  toName?: Prisma.StringFilter<"Invoice"> | string
  total?: Prisma.IntFilter<"Invoice"> | number
  type?: Prisma.StringFilter<"Invoice"> | string
  archived?: Prisma.BoolFilter<"Invoice"> | boolean
  userId?: Prisma.StringFilter<"Invoice"> | string
  contactId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  projectId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Invoice"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Invoice"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  contact?: Prisma.XOR<Prisma.ContactNullableScalarRelationFilter, Prisma.ContactWhereInput> | null
  project?: Prisma.XOR<Prisma.ProjectNullableScalarRelationFilter, Prisma.ProjectWhereInput> | null
}, "id">

export type InvoiceOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  amountDue?: Prisma.SortOrder
  amountPaid?: Prisma.SortOrder
  discount?: Prisma.SortOrder
  dueOn?: Prisma.SortOrder
  fromAddress?: Prisma.SortOrder
  fromCompany?: Prisma.SortOrder
  fromName?: Prisma.SortOrder
  fromEmail?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  issuedOn?: Prisma.SortOrder
  items?: Prisma.SortOrder
  unitsType?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  paymentDate?: Prisma.SortOrder
  logoImage?: Prisma.SortOrderInput | Prisma.SortOrder
  fee?: Prisma.SortOrder
  net?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrderInput | Prisma.SortOrder
  transactionId?: Prisma.SortOrderInput | Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  sentDate?: Prisma.SortOrder
  status?: Prisma.SortOrder
  subtotal?: Prisma.SortOrder
  tax?: Prisma.SortOrder
  taxLabel?: Prisma.SortOrder
  toAddress?: Prisma.SortOrder
  toCompany?: Prisma.SortOrder
  toEmail?: Prisma.SortOrder
  toName?: Prisma.SortOrder
  total?: Prisma.SortOrder
  type?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  contactId?: Prisma.SortOrderInput | Prisma.SortOrder
  projectId?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.InvoiceCountOrderByAggregateInput
  _avg?: Prisma.InvoiceAvgOrderByAggregateInput
  _max?: Prisma.InvoiceMaxOrderByAggregateInput
  _min?: Prisma.InvoiceMinOrderByAggregateInput
  _sum?: Prisma.InvoiceSumOrderByAggregateInput
}

export type InvoiceScalarWhereWithAggregatesInput = {
  AND?: Prisma.InvoiceScalarWhereWithAggregatesInput | Prisma.InvoiceScalarWhereWithAggregatesInput[]
  OR?: Prisma.InvoiceScalarWhereWithAggregatesInput[]
  NOT?: Prisma.InvoiceScalarWhereWithAggregatesInput | Prisma.InvoiceScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  amountDue?: Prisma.IntWithAggregatesFilter<"Invoice"> | number
  amountPaid?: Prisma.IntWithAggregatesFilter<"Invoice"> | number
  discount?: Prisma.FloatWithAggregatesFilter<"Invoice"> | number
  dueOn?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  fromAddress?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  fromCompany?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  fromName?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  fromEmail?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  invoiceNumber?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  issuedOn?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  items?: Prisma.JsonWithAggregatesFilter<"Invoice">
  unitsType?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  notes?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  paymentDate?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  logoImage?: Prisma.StringNullableWithAggregatesFilter<"Invoice"> | string | null
  fee?: Prisma.IntWithAggregatesFilter<"Invoice"> | number
  net?: Prisma.IntWithAggregatesFilter<"Invoice"> | number
  paymentMethod?: Prisma.StringNullableWithAggregatesFilter<"Invoice"> | string | null
  transactionId?: Prisma.StringNullableWithAggregatesFilter<"Invoice"> | string | null
  isStripeEnabled?: Prisma.BoolWithAggregatesFilter<"Invoice"> | boolean
  isPaypalEnabled?: Prisma.BoolWithAggregatesFilter<"Invoice"> | boolean
  isCashappEnabled?: Prisma.BoolWithAggregatesFilter<"Invoice"> | boolean
  isZelleEnabled?: Prisma.BoolWithAggregatesFilter<"Invoice"> | boolean
  sentDate?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  status?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  subtotal?: Prisma.FloatWithAggregatesFilter<"Invoice"> | number
  tax?: Prisma.FloatWithAggregatesFilter<"Invoice"> | number
  taxLabel?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  toAddress?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  toCompany?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  toEmail?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  toName?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  total?: Prisma.IntWithAggregatesFilter<"Invoice"> | number
  type?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  archived?: Prisma.BoolWithAggregatesFilter<"Invoice"> | boolean
  userId?: Prisma.StringWithAggregatesFilter<"Invoice"> | string
  contactId?: Prisma.StringNullableWithAggregatesFilter<"Invoice"> | string | null
  projectId?: Prisma.StringNullableWithAggregatesFilter<"Invoice"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Invoice"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Invoice"> | Date | string
}

export type InvoiceCreateInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutInvoicesInput
  contact?: Prisma.ContactCreateNestedOneWithoutInvoicesInput
  project?: Prisma.ProjectCreateNestedOneWithoutInvoicesInput
}

export type InvoiceUncheckedCreateInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  userId: string
  contactId?: string | null
  projectId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type InvoiceUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutInvoicesNestedInput
  contact?: Prisma.ContactUpdateOneWithoutInvoicesNestedInput
  project?: Prisma.ProjectUpdateOneWithoutInvoicesNestedInput
}

export type InvoiceUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  contactId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  projectId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvoiceCreateManyInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  userId: string
  contactId?: string | null
  projectId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type InvoiceUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvoiceUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  contactId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  projectId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvoiceListRelationFilter = {
  every?: Prisma.InvoiceWhereInput
  some?: Prisma.InvoiceWhereInput
  none?: Prisma.InvoiceWhereInput
}

export type InvoiceOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type InvoiceCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amountDue?: Prisma.SortOrder
  amountPaid?: Prisma.SortOrder
  discount?: Prisma.SortOrder
  dueOn?: Prisma.SortOrder
  fromAddress?: Prisma.SortOrder
  fromCompany?: Prisma.SortOrder
  fromName?: Prisma.SortOrder
  fromEmail?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  issuedOn?: Prisma.SortOrder
  items?: Prisma.SortOrder
  unitsType?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  paymentDate?: Prisma.SortOrder
  logoImage?: Prisma.SortOrder
  fee?: Prisma.SortOrder
  net?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  sentDate?: Prisma.SortOrder
  status?: Prisma.SortOrder
  subtotal?: Prisma.SortOrder
  tax?: Prisma.SortOrder
  taxLabel?: Prisma.SortOrder
  toAddress?: Prisma.SortOrder
  toCompany?: Prisma.SortOrder
  toEmail?: Prisma.SortOrder
  toName?: Prisma.SortOrder
  total?: Prisma.SortOrder
  type?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  contactId?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type InvoiceAvgOrderByAggregateInput = {
  amountDue?: Prisma.SortOrder
  amountPaid?: Prisma.SortOrder
  discount?: Prisma.SortOrder
  fee?: Prisma.SortOrder
  net?: Prisma.SortOrder
  subtotal?: Prisma.SortOrder
  tax?: Prisma.SortOrder
  total?: Prisma.SortOrder
}

export type InvoiceMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amountDue?: Prisma.SortOrder
  amountPaid?: Prisma.SortOrder
  discount?: Prisma.SortOrder
  dueOn?: Prisma.SortOrder
  fromAddress?: Prisma.SortOrder
  fromCompany?: Prisma.SortOrder
  fromName?: Prisma.SortOrder
  fromEmail?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  issuedOn?: Prisma.SortOrder
  unitsType?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  paymentDate?: Prisma.SortOrder
  logoImage?: Prisma.SortOrder
  fee?: Prisma.SortOrder
  net?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  sentDate?: Prisma.SortOrder
  status?: Prisma.SortOrder
  subtotal?: Prisma.SortOrder
  tax?: Prisma.SortOrder
  taxLabel?: Prisma.SortOrder
  toAddress?: Prisma.SortOrder
  toCompany?: Prisma.SortOrder
  toEmail?: Prisma.SortOrder
  toName?: Prisma.SortOrder
  total?: Prisma.SortOrder
  type?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  contactId?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type InvoiceMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  amountDue?: Prisma.SortOrder
  amountPaid?: Prisma.SortOrder
  discount?: Prisma.SortOrder
  dueOn?: Prisma.SortOrder
  fromAddress?: Prisma.SortOrder
  fromCompany?: Prisma.SortOrder
  fromName?: Prisma.SortOrder
  fromEmail?: Prisma.SortOrder
  invoiceNumber?: Prisma.SortOrder
  issuedOn?: Prisma.SortOrder
  unitsType?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  paymentDate?: Prisma.SortOrder
  logoImage?: Prisma.SortOrder
  fee?: Prisma.SortOrder
  net?: Prisma.SortOrder
  paymentMethod?: Prisma.SortOrder
  transactionId?: Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  sentDate?: Prisma.SortOrder
  status?: Prisma.SortOrder
  subtotal?: Prisma.SortOrder
  tax?: Prisma.SortOrder
  taxLabel?: Prisma.SortOrder
  toAddress?: Prisma.SortOrder
  toCompany?: Prisma.SortOrder
  toEmail?: Prisma.SortOrder
  toName?: Prisma.SortOrder
  total?: Prisma.SortOrder
  type?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  contactId?: Prisma.SortOrder
  projectId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type InvoiceSumOrderByAggregateInput = {
  amountDue?: Prisma.SortOrder
  amountPaid?: Prisma.SortOrder
  discount?: Prisma.SortOrder
  fee?: Prisma.SortOrder
  net?: Prisma.SortOrder
  subtotal?: Prisma.SortOrder
  tax?: Prisma.SortOrder
  total?: Prisma.SortOrder
}

export type InvoiceCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutUserInput, Prisma.InvoiceUncheckedCreateWithoutUserInput> | Prisma.InvoiceCreateWithoutUserInput[] | Prisma.InvoiceUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutUserInput | Prisma.InvoiceCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.InvoiceCreateManyUserInputEnvelope
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
}

export type InvoiceUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutUserInput, Prisma.InvoiceUncheckedCreateWithoutUserInput> | Prisma.InvoiceCreateWithoutUserInput[] | Prisma.InvoiceUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutUserInput | Prisma.InvoiceCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.InvoiceCreateManyUserInputEnvelope
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
}

export type InvoiceUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutUserInput, Prisma.InvoiceUncheckedCreateWithoutUserInput> | Prisma.InvoiceCreateWithoutUserInput[] | Prisma.InvoiceUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutUserInput | Prisma.InvoiceCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.InvoiceUpsertWithWhereUniqueWithoutUserInput | Prisma.InvoiceUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.InvoiceCreateManyUserInputEnvelope
  set?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  disconnect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  delete?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  update?: Prisma.InvoiceUpdateWithWhereUniqueWithoutUserInput | Prisma.InvoiceUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.InvoiceUpdateManyWithWhereWithoutUserInput | Prisma.InvoiceUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.InvoiceScalarWhereInput | Prisma.InvoiceScalarWhereInput[]
}

export type InvoiceUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutUserInput, Prisma.InvoiceUncheckedCreateWithoutUserInput> | Prisma.InvoiceCreateWithoutUserInput[] | Prisma.InvoiceUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutUserInput | Prisma.InvoiceCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.InvoiceUpsertWithWhereUniqueWithoutUserInput | Prisma.InvoiceUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.InvoiceCreateManyUserInputEnvelope
  set?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  disconnect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  delete?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  update?: Prisma.InvoiceUpdateWithWhereUniqueWithoutUserInput | Prisma.InvoiceUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.InvoiceUpdateManyWithWhereWithoutUserInput | Prisma.InvoiceUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.InvoiceScalarWhereInput | Prisma.InvoiceScalarWhereInput[]
}

export type InvoiceCreateNestedManyWithoutContactInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutContactInput, Prisma.InvoiceUncheckedCreateWithoutContactInput> | Prisma.InvoiceCreateWithoutContactInput[] | Prisma.InvoiceUncheckedCreateWithoutContactInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutContactInput | Prisma.InvoiceCreateOrConnectWithoutContactInput[]
  createMany?: Prisma.InvoiceCreateManyContactInputEnvelope
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
}

export type InvoiceUncheckedCreateNestedManyWithoutContactInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutContactInput, Prisma.InvoiceUncheckedCreateWithoutContactInput> | Prisma.InvoiceCreateWithoutContactInput[] | Prisma.InvoiceUncheckedCreateWithoutContactInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutContactInput | Prisma.InvoiceCreateOrConnectWithoutContactInput[]
  createMany?: Prisma.InvoiceCreateManyContactInputEnvelope
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
}

export type InvoiceUpdateManyWithoutContactNestedInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutContactInput, Prisma.InvoiceUncheckedCreateWithoutContactInput> | Prisma.InvoiceCreateWithoutContactInput[] | Prisma.InvoiceUncheckedCreateWithoutContactInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutContactInput | Prisma.InvoiceCreateOrConnectWithoutContactInput[]
  upsert?: Prisma.InvoiceUpsertWithWhereUniqueWithoutContactInput | Prisma.InvoiceUpsertWithWhereUniqueWithoutContactInput[]
  createMany?: Prisma.InvoiceCreateManyContactInputEnvelope
  set?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  disconnect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  delete?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  update?: Prisma.InvoiceUpdateWithWhereUniqueWithoutContactInput | Prisma.InvoiceUpdateWithWhereUniqueWithoutContactInput[]
  updateMany?: Prisma.InvoiceUpdateManyWithWhereWithoutContactInput | Prisma.InvoiceUpdateManyWithWhereWithoutContactInput[]
  deleteMany?: Prisma.InvoiceScalarWhereInput | Prisma.InvoiceScalarWhereInput[]
}

export type InvoiceUncheckedUpdateManyWithoutContactNestedInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutContactInput, Prisma.InvoiceUncheckedCreateWithoutContactInput> | Prisma.InvoiceCreateWithoutContactInput[] | Prisma.InvoiceUncheckedCreateWithoutContactInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutContactInput | Prisma.InvoiceCreateOrConnectWithoutContactInput[]
  upsert?: Prisma.InvoiceUpsertWithWhereUniqueWithoutContactInput | Prisma.InvoiceUpsertWithWhereUniqueWithoutContactInput[]
  createMany?: Prisma.InvoiceCreateManyContactInputEnvelope
  set?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  disconnect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  delete?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  update?: Prisma.InvoiceUpdateWithWhereUniqueWithoutContactInput | Prisma.InvoiceUpdateWithWhereUniqueWithoutContactInput[]
  updateMany?: Prisma.InvoiceUpdateManyWithWhereWithoutContactInput | Prisma.InvoiceUpdateManyWithWhereWithoutContactInput[]
  deleteMany?: Prisma.InvoiceScalarWhereInput | Prisma.InvoiceScalarWhereInput[]
}

export type InvoiceCreateNestedManyWithoutProjectInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutProjectInput, Prisma.InvoiceUncheckedCreateWithoutProjectInput> | Prisma.InvoiceCreateWithoutProjectInput[] | Prisma.InvoiceUncheckedCreateWithoutProjectInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutProjectInput | Prisma.InvoiceCreateOrConnectWithoutProjectInput[]
  createMany?: Prisma.InvoiceCreateManyProjectInputEnvelope
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
}

export type InvoiceUncheckedCreateNestedManyWithoutProjectInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutProjectInput, Prisma.InvoiceUncheckedCreateWithoutProjectInput> | Prisma.InvoiceCreateWithoutProjectInput[] | Prisma.InvoiceUncheckedCreateWithoutProjectInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutProjectInput | Prisma.InvoiceCreateOrConnectWithoutProjectInput[]
  createMany?: Prisma.InvoiceCreateManyProjectInputEnvelope
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
}

export type InvoiceUpdateManyWithoutProjectNestedInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutProjectInput, Prisma.InvoiceUncheckedCreateWithoutProjectInput> | Prisma.InvoiceCreateWithoutProjectInput[] | Prisma.InvoiceUncheckedCreateWithoutProjectInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutProjectInput | Prisma.InvoiceCreateOrConnectWithoutProjectInput[]
  upsert?: Prisma.InvoiceUpsertWithWhereUniqueWithoutProjectInput | Prisma.InvoiceUpsertWithWhereUniqueWithoutProjectInput[]
  createMany?: Prisma.InvoiceCreateManyProjectInputEnvelope
  set?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  disconnect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  delete?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  update?: Prisma.InvoiceUpdateWithWhereUniqueWithoutProjectInput | Prisma.InvoiceUpdateWithWhereUniqueWithoutProjectInput[]
  updateMany?: Prisma.InvoiceUpdateManyWithWhereWithoutProjectInput | Prisma.InvoiceUpdateManyWithWhereWithoutProjectInput[]
  deleteMany?: Prisma.InvoiceScalarWhereInput | Prisma.InvoiceScalarWhereInput[]
}

export type InvoiceUncheckedUpdateManyWithoutProjectNestedInput = {
  create?: Prisma.XOR<Prisma.InvoiceCreateWithoutProjectInput, Prisma.InvoiceUncheckedCreateWithoutProjectInput> | Prisma.InvoiceCreateWithoutProjectInput[] | Prisma.InvoiceUncheckedCreateWithoutProjectInput[]
  connectOrCreate?: Prisma.InvoiceCreateOrConnectWithoutProjectInput | Prisma.InvoiceCreateOrConnectWithoutProjectInput[]
  upsert?: Prisma.InvoiceUpsertWithWhereUniqueWithoutProjectInput | Prisma.InvoiceUpsertWithWhereUniqueWithoutProjectInput[]
  createMany?: Prisma.InvoiceCreateManyProjectInputEnvelope
  set?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  disconnect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  delete?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  connect?: Prisma.InvoiceWhereUniqueInput | Prisma.InvoiceWhereUniqueInput[]
  update?: Prisma.InvoiceUpdateWithWhereUniqueWithoutProjectInput | Prisma.InvoiceUpdateWithWhereUniqueWithoutProjectInput[]
  updateMany?: Prisma.InvoiceUpdateManyWithWhereWithoutProjectInput | Prisma.InvoiceUpdateManyWithWhereWithoutProjectInput[]
  deleteMany?: Prisma.InvoiceScalarWhereInput | Prisma.InvoiceScalarWhereInput[]
}

export type FloatFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type InvoiceCreateWithoutUserInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  contact?: Prisma.ContactCreateNestedOneWithoutInvoicesInput
  project?: Prisma.ProjectCreateNestedOneWithoutInvoicesInput
}

export type InvoiceUncheckedCreateWithoutUserInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  contactId?: string | null
  projectId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type InvoiceCreateOrConnectWithoutUserInput = {
  where: Prisma.InvoiceWhereUniqueInput
  create: Prisma.XOR<Prisma.InvoiceCreateWithoutUserInput, Prisma.InvoiceUncheckedCreateWithoutUserInput>
}

export type InvoiceCreateManyUserInputEnvelope = {
  data: Prisma.InvoiceCreateManyUserInput | Prisma.InvoiceCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type InvoiceUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.InvoiceWhereUniqueInput
  update: Prisma.XOR<Prisma.InvoiceUpdateWithoutUserInput, Prisma.InvoiceUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.InvoiceCreateWithoutUserInput, Prisma.InvoiceUncheckedCreateWithoutUserInput>
}

export type InvoiceUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.InvoiceWhereUniqueInput
  data: Prisma.XOR<Prisma.InvoiceUpdateWithoutUserInput, Prisma.InvoiceUncheckedUpdateWithoutUserInput>
}

export type InvoiceUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.InvoiceScalarWhereInput
  data: Prisma.XOR<Prisma.InvoiceUpdateManyMutationInput, Prisma.InvoiceUncheckedUpdateManyWithoutUserInput>
}

export type InvoiceScalarWhereInput = {
  AND?: Prisma.InvoiceScalarWhereInput | Prisma.InvoiceScalarWhereInput[]
  OR?: Prisma.InvoiceScalarWhereInput[]
  NOT?: Prisma.InvoiceScalarWhereInput | Prisma.InvoiceScalarWhereInput[]
  id?: Prisma.StringFilter<"Invoice"> | string
  amountDue?: Prisma.IntFilter<"Invoice"> | number
  amountPaid?: Prisma.IntFilter<"Invoice"> | number
  discount?: Prisma.FloatFilter<"Invoice"> | number
  dueOn?: Prisma.StringFilter<"Invoice"> | string
  fromAddress?: Prisma.StringFilter<"Invoice"> | string
  fromCompany?: Prisma.StringFilter<"Invoice"> | string
  fromName?: Prisma.StringFilter<"Invoice"> | string
  fromEmail?: Prisma.StringFilter<"Invoice"> | string
  invoiceNumber?: Prisma.StringFilter<"Invoice"> | string
  issuedOn?: Prisma.StringFilter<"Invoice"> | string
  items?: Prisma.JsonFilter<"Invoice">
  unitsType?: Prisma.StringFilter<"Invoice"> | string
  notes?: Prisma.StringFilter<"Invoice"> | string
  paymentDate?: Prisma.StringFilter<"Invoice"> | string
  logoImage?: Prisma.StringNullableFilter<"Invoice"> | string | null
  fee?: Prisma.IntFilter<"Invoice"> | number
  net?: Prisma.IntFilter<"Invoice"> | number
  paymentMethod?: Prisma.StringNullableFilter<"Invoice"> | string | null
  transactionId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  isStripeEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isPaypalEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isCashappEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  isZelleEnabled?: Prisma.BoolFilter<"Invoice"> | boolean
  sentDate?: Prisma.StringFilter<"Invoice"> | string
  status?: Prisma.StringFilter<"Invoice"> | string
  subtotal?: Prisma.FloatFilter<"Invoice"> | number
  tax?: Prisma.FloatFilter<"Invoice"> | number
  taxLabel?: Prisma.StringFilter<"Invoice"> | string
  toAddress?: Prisma.StringFilter<"Invoice"> | string
  toCompany?: Prisma.StringFilter<"Invoice"> | string
  toEmail?: Prisma.StringFilter<"Invoice"> | string
  toName?: Prisma.StringFilter<"Invoice"> | string
  total?: Prisma.IntFilter<"Invoice"> | number
  type?: Prisma.StringFilter<"Invoice"> | string
  archived?: Prisma.BoolFilter<"Invoice"> | boolean
  userId?: Prisma.StringFilter<"Invoice"> | string
  contactId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  projectId?: Prisma.StringNullableFilter<"Invoice"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Invoice"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Invoice"> | Date | string
}

export type InvoiceCreateWithoutContactInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutInvoicesInput
  project?: Prisma.ProjectCreateNestedOneWithoutInvoicesInput
}

export type InvoiceUncheckedCreateWithoutContactInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  userId: string
  projectId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type InvoiceCreateOrConnectWithoutContactInput = {
  where: Prisma.InvoiceWhereUniqueInput
  create: Prisma.XOR<Prisma.InvoiceCreateWithoutContactInput, Prisma.InvoiceUncheckedCreateWithoutContactInput>
}

export type InvoiceCreateManyContactInputEnvelope = {
  data: Prisma.InvoiceCreateManyContactInput | Prisma.InvoiceCreateManyContactInput[]
  skipDuplicates?: boolean
}

export type InvoiceUpsertWithWhereUniqueWithoutContactInput = {
  where: Prisma.InvoiceWhereUniqueInput
  update: Prisma.XOR<Prisma.InvoiceUpdateWithoutContactInput, Prisma.InvoiceUncheckedUpdateWithoutContactInput>
  create: Prisma.XOR<Prisma.InvoiceCreateWithoutContactInput, Prisma.InvoiceUncheckedCreateWithoutContactInput>
}

export type InvoiceUpdateWithWhereUniqueWithoutContactInput = {
  where: Prisma.InvoiceWhereUniqueInput
  data: Prisma.XOR<Prisma.InvoiceUpdateWithoutContactInput, Prisma.InvoiceUncheckedUpdateWithoutContactInput>
}

export type InvoiceUpdateManyWithWhereWithoutContactInput = {
  where: Prisma.InvoiceScalarWhereInput
  data: Prisma.XOR<Prisma.InvoiceUpdateManyMutationInput, Prisma.InvoiceUncheckedUpdateManyWithoutContactInput>
}

export type InvoiceCreateWithoutProjectInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutInvoicesInput
  contact?: Prisma.ContactCreateNestedOneWithoutInvoicesInput
}

export type InvoiceUncheckedCreateWithoutProjectInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  userId: string
  contactId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type InvoiceCreateOrConnectWithoutProjectInput = {
  where: Prisma.InvoiceWhereUniqueInput
  create: Prisma.XOR<Prisma.InvoiceCreateWithoutProjectInput, Prisma.InvoiceUncheckedCreateWithoutProjectInput>
}

export type InvoiceCreateManyProjectInputEnvelope = {
  data: Prisma.InvoiceCreateManyProjectInput | Prisma.InvoiceCreateManyProjectInput[]
  skipDuplicates?: boolean
}

export type InvoiceUpsertWithWhereUniqueWithoutProjectInput = {
  where: Prisma.InvoiceWhereUniqueInput
  update: Prisma.XOR<Prisma.InvoiceUpdateWithoutProjectInput, Prisma.InvoiceUncheckedUpdateWithoutProjectInput>
  create: Prisma.XOR<Prisma.InvoiceCreateWithoutProjectInput, Prisma.InvoiceUncheckedCreateWithoutProjectInput>
}

export type InvoiceUpdateWithWhereUniqueWithoutProjectInput = {
  where: Prisma.InvoiceWhereUniqueInput
  data: Prisma.XOR<Prisma.InvoiceUpdateWithoutProjectInput, Prisma.InvoiceUncheckedUpdateWithoutProjectInput>
}

export type InvoiceUpdateManyWithWhereWithoutProjectInput = {
  where: Prisma.InvoiceScalarWhereInput
  data: Prisma.XOR<Prisma.InvoiceUpdateManyMutationInput, Prisma.InvoiceUncheckedUpdateManyWithoutProjectInput>
}

export type InvoiceCreateManyUserInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  contactId?: string | null
  projectId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type InvoiceUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  contact?: Prisma.ContactUpdateOneWithoutInvoicesNestedInput
  project?: Prisma.ProjectUpdateOneWithoutInvoicesNestedInput
}

export type InvoiceUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  contactId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  projectId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvoiceUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  contactId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  projectId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvoiceCreateManyContactInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  userId: string
  projectId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type InvoiceUpdateWithoutContactInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutInvoicesNestedInput
  project?: Prisma.ProjectUpdateOneWithoutInvoicesNestedInput
}

export type InvoiceUncheckedUpdateWithoutContactInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvoiceUncheckedUpdateManyWithoutContactInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  projectId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvoiceCreateManyProjectInput = {
  id?: string
  amountDue?: number
  amountPaid?: number
  discount?: number
  dueOn?: string
  fromAddress?: string
  fromCompany?: string
  fromName?: string
  fromEmail?: string
  invoiceNumber?: string
  issuedOn?: string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: string
  notes?: string
  paymentDate?: string
  logoImage?: string | null
  fee?: number
  net?: number
  paymentMethod?: string | null
  transactionId?: string | null
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: string
  status?: string
  subtotal?: number
  tax?: number
  taxLabel?: string
  toAddress?: string
  toCompany?: string
  toEmail?: string
  toName?: string
  total?: number
  type?: string
  archived?: boolean
  userId: string
  contactId?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type InvoiceUpdateWithoutProjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutInvoicesNestedInput
  contact?: Prisma.ContactUpdateOneWithoutInvoicesNestedInput
}

export type InvoiceUncheckedUpdateWithoutProjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  contactId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type InvoiceUncheckedUpdateManyWithoutProjectInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  amountDue?: Prisma.IntFieldUpdateOperationsInput | number
  amountPaid?: Prisma.IntFieldUpdateOperationsInput | number
  discount?: Prisma.FloatFieldUpdateOperationsInput | number
  dueOn?: Prisma.StringFieldUpdateOperationsInput | string
  fromAddress?: Prisma.StringFieldUpdateOperationsInput | string
  fromCompany?: Prisma.StringFieldUpdateOperationsInput | string
  fromName?: Prisma.StringFieldUpdateOperationsInput | string
  fromEmail?: Prisma.StringFieldUpdateOperationsInput | string
  invoiceNumber?: Prisma.StringFieldUpdateOperationsInput | string
  issuedOn?: Prisma.StringFieldUpdateOperationsInput | string
  items?: Prisma.JsonNullValueInput | runtime.InputJsonValue
  unitsType?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  paymentDate?: Prisma.StringFieldUpdateOperationsInput | string
  logoImage?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fee?: Prisma.IntFieldUpdateOperationsInput | number
  net?: Prisma.IntFieldUpdateOperationsInput | number
  paymentMethod?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  transactionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  sentDate?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.StringFieldUpdateOperationsInput | string
  subtotal?: Prisma.FloatFieldUpdateOperationsInput | number
  tax?: Prisma.FloatFieldUpdateOperationsInput | number
  taxLabel?: Prisma.StringFieldUpdateOperationsInput | string
  toAddress?: Prisma.StringFieldUpdateOperationsInput | string
  toCompany?: Prisma.StringFieldUpdateOperationsInput | string
  toEmail?: Prisma.StringFieldUpdateOperationsInput | string
  toName?: Prisma.StringFieldUpdateOperationsInput | string
  total?: Prisma.IntFieldUpdateOperationsInput | number
  type?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  contactId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type InvoiceSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  amountDue?: boolean
  amountPaid?: boolean
  discount?: boolean
  dueOn?: boolean
  fromAddress?: boolean
  fromCompany?: boolean
  fromName?: boolean
  fromEmail?: boolean
  invoiceNumber?: boolean
  issuedOn?: boolean
  items?: boolean
  unitsType?: boolean
  notes?: boolean
  paymentDate?: boolean
  logoImage?: boolean
  fee?: boolean
  net?: boolean
  paymentMethod?: boolean
  transactionId?: boolean
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: boolean
  status?: boolean
  subtotal?: boolean
  tax?: boolean
  taxLabel?: boolean
  toAddress?: boolean
  toCompany?: boolean
  toEmail?: boolean
  toName?: boolean
  total?: boolean
  type?: boolean
  archived?: boolean
  userId?: boolean
  contactId?: boolean
  projectId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  contact?: boolean | Prisma.Invoice$contactArgs<ExtArgs>
  project?: boolean | Prisma.Invoice$projectArgs<ExtArgs>
}, ExtArgs["result"]["invoice"]>

export type InvoiceSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  amountDue?: boolean
  amountPaid?: boolean
  discount?: boolean
  dueOn?: boolean
  fromAddress?: boolean
  fromCompany?: boolean
  fromName?: boolean
  fromEmail?: boolean
  invoiceNumber?: boolean
  issuedOn?: boolean
  items?: boolean
  unitsType?: boolean
  notes?: boolean
  paymentDate?: boolean
  logoImage?: boolean
  fee?: boolean
  net?: boolean
  paymentMethod?: boolean
  transactionId?: boolean
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: boolean
  status?: boolean
  subtotal?: boolean
  tax?: boolean
  taxLabel?: boolean
  toAddress?: boolean
  toCompany?: boolean
  toEmail?: boolean
  toName?: boolean
  total?: boolean
  type?: boolean
  archived?: boolean
  userId?: boolean
  contactId?: boolean
  projectId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  contact?: boolean | Prisma.Invoice$contactArgs<ExtArgs>
  project?: boolean | Prisma.Invoice$projectArgs<ExtArgs>
}, ExtArgs["result"]["invoice"]>

export type InvoiceSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  amountDue?: boolean
  amountPaid?: boolean
  discount?: boolean
  dueOn?: boolean
  fromAddress?: boolean
  fromCompany?: boolean
  fromName?: boolean
  fromEmail?: boolean
  invoiceNumber?: boolean
  issuedOn?: boolean
  items?: boolean
  unitsType?: boolean
  notes?: boolean
  paymentDate?: boolean
  logoImage?: boolean
  fee?: boolean
  net?: boolean
  paymentMethod?: boolean
  transactionId?: boolean
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: boolean
  status?: boolean
  subtotal?: boolean
  tax?: boolean
  taxLabel?: boolean
  toAddress?: boolean
  toCompany?: boolean
  toEmail?: boolean
  toName?: boolean
  total?: boolean
  type?: boolean
  archived?: boolean
  userId?: boolean
  contactId?: boolean
  projectId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  contact?: boolean | Prisma.Invoice$contactArgs<ExtArgs>
  project?: boolean | Prisma.Invoice$projectArgs<ExtArgs>
}, ExtArgs["result"]["invoice"]>

export type InvoiceSelectScalar = {
  id?: boolean
  amountDue?: boolean
  amountPaid?: boolean
  discount?: boolean
  dueOn?: boolean
  fromAddress?: boolean
  fromCompany?: boolean
  fromName?: boolean
  fromEmail?: boolean
  invoiceNumber?: boolean
  issuedOn?: boolean
  items?: boolean
  unitsType?: boolean
  notes?: boolean
  paymentDate?: boolean
  logoImage?: boolean
  fee?: boolean
  net?: boolean
  paymentMethod?: boolean
  transactionId?: boolean
  isStripeEnabled?: boolean
  isPaypalEnabled?: boolean
  isCashappEnabled?: boolean
  isZelleEnabled?: boolean
  sentDate?: boolean
  status?: boolean
  subtotal?: boolean
  tax?: boolean
  taxLabel?: boolean
  toAddress?: boolean
  toCompany?: boolean
  toEmail?: boolean
  toName?: boolean
  total?: boolean
  type?: boolean
  archived?: boolean
  userId?: boolean
  contactId?: boolean
  projectId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type InvoiceOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "amountDue" | "amountPaid" | "discount" | "dueOn" | "fromAddress" | "fromCompany" | "fromName" | "fromEmail" | "invoiceNumber" | "issuedOn" | "items" | "unitsType" | "notes" | "paymentDate" | "logoImage" | "fee" | "net" | "paymentMethod" | "transactionId" | "isStripeEnabled" | "isPaypalEnabled" | "isCashappEnabled" | "isZelleEnabled" | "sentDate" | "status" | "subtotal" | "tax" | "taxLabel" | "toAddress" | "toCompany" | "toEmail" | "toName" | "total" | "type" | "archived" | "userId" | "contactId" | "projectId" | "createdAt" | "updatedAt", ExtArgs["result"]["invoice"]>
export type InvoiceInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  contact?: boolean | Prisma.Invoice$contactArgs<ExtArgs>
  project?: boolean | Prisma.Invoice$projectArgs<ExtArgs>
}
export type InvoiceIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  contact?: boolean | Prisma.Invoice$contactArgs<ExtArgs>
  project?: boolean | Prisma.Invoice$projectArgs<ExtArgs>
}
export type InvoiceIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  contact?: boolean | Prisma.Invoice$contactArgs<ExtArgs>
  project?: boolean | Prisma.Invoice$projectArgs<ExtArgs>
}

export type $InvoicePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Invoice"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
    contact: Prisma.$ContactPayload<ExtArgs> | null
    project: Prisma.$ProjectPayload<ExtArgs> | null
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    amountDue: number
    amountPaid: number
    discount: number
    dueOn: string
    fromAddress: string
    fromCompany: string
    fromName: string
    fromEmail: string
    invoiceNumber: string
    issuedOn: string
    items: runtime.JsonValue
    unitsType: string
    notes: string
    paymentDate: string
    logoImage: string | null
    fee: number
    net: number
    paymentMethod: string | null
    transactionId: string | null
    isStripeEnabled: boolean
    isPaypalEnabled: boolean
    isCashappEnabled: boolean
    isZelleEnabled: boolean
    sentDate: string
    status: string
    subtotal: number
    tax: number
    taxLabel: string
    toAddress: string
    toCompany: string
    toEmail: string
    toName: string
    total: number
    type: string
    archived: boolean
    userId: string
    contactId: string | null
    projectId: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["invoice"]>
  composites: {}
}

export type InvoiceGetPayload<S extends boolean | null | undefined | InvoiceDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$InvoicePayload, S>

export type InvoiceCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<InvoiceFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: InvoiceCountAggregateInputType | true
  }

export interface InvoiceDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Invoice'], meta: { name: 'Invoice' } }
  /**
   * Find zero or one Invoice that matches the filter.
   * @param {InvoiceFindUniqueArgs} args - Arguments to find a Invoice
   * @example
   * // Get one Invoice
   * const invoice = await prisma.invoice.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends InvoiceFindUniqueArgs>(args: Prisma.SelectSubset<T, InvoiceFindUniqueArgs<ExtArgs>>): Prisma.Prisma__InvoiceClient<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Invoice that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {InvoiceFindUniqueOrThrowArgs} args - Arguments to find a Invoice
   * @example
   * // Get one Invoice
   * const invoice = await prisma.invoice.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends InvoiceFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, InvoiceFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__InvoiceClient<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Invoice that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvoiceFindFirstArgs} args - Arguments to find a Invoice
   * @example
   * // Get one Invoice
   * const invoice = await prisma.invoice.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends InvoiceFindFirstArgs>(args?: Prisma.SelectSubset<T, InvoiceFindFirstArgs<ExtArgs>>): Prisma.Prisma__InvoiceClient<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Invoice that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvoiceFindFirstOrThrowArgs} args - Arguments to find a Invoice
   * @example
   * // Get one Invoice
   * const invoice = await prisma.invoice.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends InvoiceFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, InvoiceFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__InvoiceClient<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Invoices that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvoiceFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Invoices
   * const invoices = await prisma.invoice.findMany()
   * 
   * // Get first 10 Invoices
   * const invoices = await prisma.invoice.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const invoiceWithIdOnly = await prisma.invoice.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends InvoiceFindManyArgs>(args?: Prisma.SelectSubset<T, InvoiceFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Invoice.
   * @param {InvoiceCreateArgs} args - Arguments to create a Invoice.
   * @example
   * // Create one Invoice
   * const Invoice = await prisma.invoice.create({
   *   data: {
   *     // ... data to create a Invoice
   *   }
   * })
   * 
   */
  create<T extends InvoiceCreateArgs>(args: Prisma.SelectSubset<T, InvoiceCreateArgs<ExtArgs>>): Prisma.Prisma__InvoiceClient<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Invoices.
   * @param {InvoiceCreateManyArgs} args - Arguments to create many Invoices.
   * @example
   * // Create many Invoices
   * const invoice = await prisma.invoice.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends InvoiceCreateManyArgs>(args?: Prisma.SelectSubset<T, InvoiceCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Invoices and returns the data saved in the database.
   * @param {InvoiceCreateManyAndReturnArgs} args - Arguments to create many Invoices.
   * @example
   * // Create many Invoices
   * const invoice = await prisma.invoice.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Invoices and only return the `id`
   * const invoiceWithIdOnly = await prisma.invoice.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends InvoiceCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, InvoiceCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Invoice.
   * @param {InvoiceDeleteArgs} args - Arguments to delete one Invoice.
   * @example
   * // Delete one Invoice
   * const Invoice = await prisma.invoice.delete({
   *   where: {
   *     // ... filter to delete one Invoice
   *   }
   * })
   * 
   */
  delete<T extends InvoiceDeleteArgs>(args: Prisma.SelectSubset<T, InvoiceDeleteArgs<ExtArgs>>): Prisma.Prisma__InvoiceClient<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Invoice.
   * @param {InvoiceUpdateArgs} args - Arguments to update one Invoice.
   * @example
   * // Update one Invoice
   * const invoice = await prisma.invoice.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends InvoiceUpdateArgs>(args: Prisma.SelectSubset<T, InvoiceUpdateArgs<ExtArgs>>): Prisma.Prisma__InvoiceClient<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Invoices.
   * @param {InvoiceDeleteManyArgs} args - Arguments to filter Invoices to delete.
   * @example
   * // Delete a few Invoices
   * const { count } = await prisma.invoice.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends InvoiceDeleteManyArgs>(args?: Prisma.SelectSubset<T, InvoiceDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Invoices.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvoiceUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Invoices
   * const invoice = await prisma.invoice.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends InvoiceUpdateManyArgs>(args: Prisma.SelectSubset<T, InvoiceUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Invoices and returns the data updated in the database.
   * @param {InvoiceUpdateManyAndReturnArgs} args - Arguments to update many Invoices.
   * @example
   * // Update many Invoices
   * const invoice = await prisma.invoice.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Invoices and only return the `id`
   * const invoiceWithIdOnly = await prisma.invoice.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends InvoiceUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, InvoiceUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Invoice.
   * @param {InvoiceUpsertArgs} args - Arguments to update or create a Invoice.
   * @example
   * // Update or create a Invoice
   * const invoice = await prisma.invoice.upsert({
   *   create: {
   *     // ... data to create a Invoice
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Invoice we want to update
   *   }
   * })
   */
  upsert<T extends InvoiceUpsertArgs>(args: Prisma.SelectSubset<T, InvoiceUpsertArgs<ExtArgs>>): Prisma.Prisma__InvoiceClient<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Invoices.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvoiceCountArgs} args - Arguments to filter Invoices to count.
   * @example
   * // Count the number of Invoices
   * const count = await prisma.invoice.count({
   *   where: {
   *     // ... the filter for the Invoices we want to count
   *   }
   * })
  **/
  count<T extends InvoiceCountArgs>(
    args?: Prisma.Subset<T, InvoiceCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], InvoiceCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Invoice.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvoiceAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends InvoiceAggregateArgs>(args: Prisma.Subset<T, InvoiceAggregateArgs>): Prisma.PrismaPromise<GetInvoiceAggregateType<T>>

  /**
   * Group by Invoice.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {InvoiceGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends InvoiceGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: InvoiceGroupByArgs['orderBy'] }
      : { orderBy?: InvoiceGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, InvoiceGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetInvoiceGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Invoice model
 */
readonly fields: InvoiceFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Invoice.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__InvoiceClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  contact<T extends Prisma.Invoice$contactArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Invoice$contactArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  project<T extends Prisma.Invoice$projectArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Invoice$projectArgs<ExtArgs>>): Prisma.Prisma__ProjectClient<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Invoice model
 */
export interface InvoiceFieldRefs {
  readonly id: Prisma.FieldRef<"Invoice", 'String'>
  readonly amountDue: Prisma.FieldRef<"Invoice", 'Int'>
  readonly amountPaid: Prisma.FieldRef<"Invoice", 'Int'>
  readonly discount: Prisma.FieldRef<"Invoice", 'Float'>
  readonly dueOn: Prisma.FieldRef<"Invoice", 'String'>
  readonly fromAddress: Prisma.FieldRef<"Invoice", 'String'>
  readonly fromCompany: Prisma.FieldRef<"Invoice", 'String'>
  readonly fromName: Prisma.FieldRef<"Invoice", 'String'>
  readonly fromEmail: Prisma.FieldRef<"Invoice", 'String'>
  readonly invoiceNumber: Prisma.FieldRef<"Invoice", 'String'>
  readonly issuedOn: Prisma.FieldRef<"Invoice", 'String'>
  readonly items: Prisma.FieldRef<"Invoice", 'Json'>
  readonly unitsType: Prisma.FieldRef<"Invoice", 'String'>
  readonly notes: Prisma.FieldRef<"Invoice", 'String'>
  readonly paymentDate: Prisma.FieldRef<"Invoice", 'String'>
  readonly logoImage: Prisma.FieldRef<"Invoice", 'String'>
  readonly fee: Prisma.FieldRef<"Invoice", 'Int'>
  readonly net: Prisma.FieldRef<"Invoice", 'Int'>
  readonly paymentMethod: Prisma.FieldRef<"Invoice", 'String'>
  readonly transactionId: Prisma.FieldRef<"Invoice", 'String'>
  readonly isStripeEnabled: Prisma.FieldRef<"Invoice", 'Boolean'>
  readonly isPaypalEnabled: Prisma.FieldRef<"Invoice", 'Boolean'>
  readonly isCashappEnabled: Prisma.FieldRef<"Invoice", 'Boolean'>
  readonly isZelleEnabled: Prisma.FieldRef<"Invoice", 'Boolean'>
  readonly sentDate: Prisma.FieldRef<"Invoice", 'String'>
  readonly status: Prisma.FieldRef<"Invoice", 'String'>
  readonly subtotal: Prisma.FieldRef<"Invoice", 'Float'>
  readonly tax: Prisma.FieldRef<"Invoice", 'Float'>
  readonly taxLabel: Prisma.FieldRef<"Invoice", 'String'>
  readonly toAddress: Prisma.FieldRef<"Invoice", 'String'>
  readonly toCompany: Prisma.FieldRef<"Invoice", 'String'>
  readonly toEmail: Prisma.FieldRef<"Invoice", 'String'>
  readonly toName: Prisma.FieldRef<"Invoice", 'String'>
  readonly total: Prisma.FieldRef<"Invoice", 'Int'>
  readonly type: Prisma.FieldRef<"Invoice", 'String'>
  readonly archived: Prisma.FieldRef<"Invoice", 'Boolean'>
  readonly userId: Prisma.FieldRef<"Invoice", 'String'>
  readonly contactId: Prisma.FieldRef<"Invoice", 'String'>
  readonly projectId: Prisma.FieldRef<"Invoice", 'String'>
  readonly createdAt: Prisma.FieldRef<"Invoice", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Invoice", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Invoice findUnique
 */
export type InvoiceFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * Filter, which Invoice to fetch.
   */
  where: Prisma.InvoiceWhereUniqueInput
}

/**
 * Invoice findUniqueOrThrow
 */
export type InvoiceFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * Filter, which Invoice to fetch.
   */
  where: Prisma.InvoiceWhereUniqueInput
}

/**
 * Invoice findFirst
 */
export type InvoiceFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * Filter, which Invoice to fetch.
   */
  where?: Prisma.InvoiceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Invoices to fetch.
   */
  orderBy?: Prisma.InvoiceOrderByWithRelationInput | Prisma.InvoiceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Invoices.
   */
  cursor?: Prisma.InvoiceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Invoices from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Invoices.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Invoices.
   */
  distinct?: Prisma.InvoiceScalarFieldEnum | Prisma.InvoiceScalarFieldEnum[]
}

/**
 * Invoice findFirstOrThrow
 */
export type InvoiceFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * Filter, which Invoice to fetch.
   */
  where?: Prisma.InvoiceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Invoices to fetch.
   */
  orderBy?: Prisma.InvoiceOrderByWithRelationInput | Prisma.InvoiceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Invoices.
   */
  cursor?: Prisma.InvoiceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Invoices from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Invoices.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Invoices.
   */
  distinct?: Prisma.InvoiceScalarFieldEnum | Prisma.InvoiceScalarFieldEnum[]
}

/**
 * Invoice findMany
 */
export type InvoiceFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * Filter, which Invoices to fetch.
   */
  where?: Prisma.InvoiceWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Invoices to fetch.
   */
  orderBy?: Prisma.InvoiceOrderByWithRelationInput | Prisma.InvoiceOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Invoices.
   */
  cursor?: Prisma.InvoiceWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Invoices from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Invoices.
   */
  skip?: number
  distinct?: Prisma.InvoiceScalarFieldEnum | Prisma.InvoiceScalarFieldEnum[]
}

/**
 * Invoice create
 */
export type InvoiceCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * The data needed to create a Invoice.
   */
  data: Prisma.XOR<Prisma.InvoiceCreateInput, Prisma.InvoiceUncheckedCreateInput>
}

/**
 * Invoice createMany
 */
export type InvoiceCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Invoices.
   */
  data: Prisma.InvoiceCreateManyInput | Prisma.InvoiceCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Invoice createManyAndReturn
 */
export type InvoiceCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * The data used to create many Invoices.
   */
  data: Prisma.InvoiceCreateManyInput | Prisma.InvoiceCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Invoice update
 */
export type InvoiceUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * The data needed to update a Invoice.
   */
  data: Prisma.XOR<Prisma.InvoiceUpdateInput, Prisma.InvoiceUncheckedUpdateInput>
  /**
   * Choose, which Invoice to update.
   */
  where: Prisma.InvoiceWhereUniqueInput
}

/**
 * Invoice updateMany
 */
export type InvoiceUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Invoices.
   */
  data: Prisma.XOR<Prisma.InvoiceUpdateManyMutationInput, Prisma.InvoiceUncheckedUpdateManyInput>
  /**
   * Filter which Invoices to update
   */
  where?: Prisma.InvoiceWhereInput
  /**
   * Limit how many Invoices to update.
   */
  limit?: number
}

/**
 * Invoice updateManyAndReturn
 */
export type InvoiceUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * The data used to update Invoices.
   */
  data: Prisma.XOR<Prisma.InvoiceUpdateManyMutationInput, Prisma.InvoiceUncheckedUpdateManyInput>
  /**
   * Filter which Invoices to update
   */
  where?: Prisma.InvoiceWhereInput
  /**
   * Limit how many Invoices to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Invoice upsert
 */
export type InvoiceUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * The filter to search for the Invoice to update in case it exists.
   */
  where: Prisma.InvoiceWhereUniqueInput
  /**
   * In case the Invoice found by the `where` argument doesn't exist, create a new Invoice with this data.
   */
  create: Prisma.XOR<Prisma.InvoiceCreateInput, Prisma.InvoiceUncheckedCreateInput>
  /**
   * In case the Invoice was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.InvoiceUpdateInput, Prisma.InvoiceUncheckedUpdateInput>
}

/**
 * Invoice delete
 */
export type InvoiceDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  /**
   * Filter which Invoice to delete.
   */
  where: Prisma.InvoiceWhereUniqueInput
}

/**
 * Invoice deleteMany
 */
export type InvoiceDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Invoices to delete
   */
  where?: Prisma.InvoiceWhereInput
  /**
   * Limit how many Invoices to delete.
   */
  limit?: number
}

/**
 * Invoice.contact
 */
export type Invoice$contactArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  where?: Prisma.ContactWhereInput
}

/**
 * Invoice.project
 */
export type Invoice$projectArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  where?: Prisma.ProjectWhereInput
}

/**
 * Invoice without action
 */
export type InvoiceDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
}
