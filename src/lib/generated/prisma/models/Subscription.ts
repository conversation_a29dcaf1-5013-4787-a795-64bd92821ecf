
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Subscription` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Subscription
 * 
 */
export type SubscriptionModel = runtime.Types.Result.DefaultSelection<Prisma.$SubscriptionPayload>

export type AggregateSubscription = {
  _count: SubscriptionCountAggregateOutputType | null
  _avg: SubscriptionAvgAggregateOutputType | null
  _sum: SubscriptionSumAggregateOutputType | null
  _min: SubscriptionMinAggregateOutputType | null
  _max: SubscriptionMaxAggregateOutputType | null
}

export type SubscriptionAvgAggregateOutputType = {
  seats: number | null
}

export type SubscriptionSumAggregateOutputType = {
  seats: number | null
}

export type SubscriptionMinAggregateOutputType = {
  id: string | null
  plan: string | null
  referenceId: string | null
  stripeCustomerId: string | null
  stripeSubscriptionId: string | null
  status: string | null
  periodStart: Date | null
  periodEnd: Date | null
  cancelAtPeriodEnd: boolean | null
  seats: number | null
  trialStart: Date | null
  trialEnd: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type SubscriptionMaxAggregateOutputType = {
  id: string | null
  plan: string | null
  referenceId: string | null
  stripeCustomerId: string | null
  stripeSubscriptionId: string | null
  status: string | null
  periodStart: Date | null
  periodEnd: Date | null
  cancelAtPeriodEnd: boolean | null
  seats: number | null
  trialStart: Date | null
  trialEnd: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type SubscriptionCountAggregateOutputType = {
  id: number
  plan: number
  referenceId: number
  stripeCustomerId: number
  stripeSubscriptionId: number
  status: number
  periodStart: number
  periodEnd: number
  cancelAtPeriodEnd: number
  seats: number
  trialStart: number
  trialEnd: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type SubscriptionAvgAggregateInputType = {
  seats?: true
}

export type SubscriptionSumAggregateInputType = {
  seats?: true
}

export type SubscriptionMinAggregateInputType = {
  id?: true
  plan?: true
  referenceId?: true
  stripeCustomerId?: true
  stripeSubscriptionId?: true
  status?: true
  periodStart?: true
  periodEnd?: true
  cancelAtPeriodEnd?: true
  seats?: true
  trialStart?: true
  trialEnd?: true
  createdAt?: true
  updatedAt?: true
}

export type SubscriptionMaxAggregateInputType = {
  id?: true
  plan?: true
  referenceId?: true
  stripeCustomerId?: true
  stripeSubscriptionId?: true
  status?: true
  periodStart?: true
  periodEnd?: true
  cancelAtPeriodEnd?: true
  seats?: true
  trialStart?: true
  trialEnd?: true
  createdAt?: true
  updatedAt?: true
}

export type SubscriptionCountAggregateInputType = {
  id?: true
  plan?: true
  referenceId?: true
  stripeCustomerId?: true
  stripeSubscriptionId?: true
  status?: true
  periodStart?: true
  periodEnd?: true
  cancelAtPeriodEnd?: true
  seats?: true
  trialStart?: true
  trialEnd?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type SubscriptionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Subscription to aggregate.
   */
  where?: Prisma.SubscriptionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Subscriptions to fetch.
   */
  orderBy?: Prisma.SubscriptionOrderByWithRelationInput | Prisma.SubscriptionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.SubscriptionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Subscriptions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Subscriptions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Subscriptions
  **/
  _count?: true | SubscriptionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: SubscriptionAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: SubscriptionSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: SubscriptionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: SubscriptionMaxAggregateInputType
}

export type GetSubscriptionAggregateType<T extends SubscriptionAggregateArgs> = {
      [P in keyof T & keyof AggregateSubscription]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSubscription[P]>
    : Prisma.GetScalarType<T[P], AggregateSubscription[P]>
}




export type SubscriptionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SubscriptionWhereInput
  orderBy?: Prisma.SubscriptionOrderByWithAggregationInput | Prisma.SubscriptionOrderByWithAggregationInput[]
  by: Prisma.SubscriptionScalarFieldEnum[] | Prisma.SubscriptionScalarFieldEnum
  having?: Prisma.SubscriptionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SubscriptionCountAggregateInputType | true
  _avg?: SubscriptionAvgAggregateInputType
  _sum?: SubscriptionSumAggregateInputType
  _min?: SubscriptionMinAggregateInputType
  _max?: SubscriptionMaxAggregateInputType
}

export type SubscriptionGroupByOutputType = {
  id: string
  plan: string
  referenceId: string
  stripeCustomerId: string | null
  stripeSubscriptionId: string | null
  status: string
  periodStart: Date | null
  periodEnd: Date | null
  cancelAtPeriodEnd: boolean | null
  seats: number | null
  trialStart: Date | null
  trialEnd: Date | null
  createdAt: Date
  updatedAt: Date
  _count: SubscriptionCountAggregateOutputType | null
  _avg: SubscriptionAvgAggregateOutputType | null
  _sum: SubscriptionSumAggregateOutputType | null
  _min: SubscriptionMinAggregateOutputType | null
  _max: SubscriptionMaxAggregateOutputType | null
}

type GetSubscriptionGroupByPayload<T extends SubscriptionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SubscriptionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof SubscriptionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], SubscriptionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], SubscriptionGroupByOutputType[P]>
      }
    >
  > 



export type SubscriptionWhereInput = {
  AND?: Prisma.SubscriptionWhereInput | Prisma.SubscriptionWhereInput[]
  OR?: Prisma.SubscriptionWhereInput[]
  NOT?: Prisma.SubscriptionWhereInput | Prisma.SubscriptionWhereInput[]
  id?: Prisma.StringFilter<"Subscription"> | string
  plan?: Prisma.StringFilter<"Subscription"> | string
  referenceId?: Prisma.StringFilter<"Subscription"> | string
  stripeCustomerId?: Prisma.StringNullableFilter<"Subscription"> | string | null
  stripeSubscriptionId?: Prisma.StringNullableFilter<"Subscription"> | string | null
  status?: Prisma.StringFilter<"Subscription"> | string
  periodStart?: Prisma.DateTimeNullableFilter<"Subscription"> | Date | string | null
  periodEnd?: Prisma.DateTimeNullableFilter<"Subscription"> | Date | string | null
  cancelAtPeriodEnd?: Prisma.BoolNullableFilter<"Subscription"> | boolean | null
  seats?: Prisma.IntNullableFilter<"Subscription"> | number | null
  trialStart?: Prisma.DateTimeNullableFilter<"Subscription"> | Date | string | null
  trialEnd?: Prisma.DateTimeNullableFilter<"Subscription"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"Subscription"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Subscription"> | Date | string
}

export type SubscriptionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  plan?: Prisma.SortOrder
  referenceId?: Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrderInput | Prisma.SortOrder
  stripeSubscriptionId?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  periodStart?: Prisma.SortOrderInput | Prisma.SortOrder
  periodEnd?: Prisma.SortOrderInput | Prisma.SortOrder
  cancelAtPeriodEnd?: Prisma.SortOrderInput | Prisma.SortOrder
  seats?: Prisma.SortOrderInput | Prisma.SortOrder
  trialStart?: Prisma.SortOrderInput | Prisma.SortOrder
  trialEnd?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type SubscriptionWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.SubscriptionWhereInput | Prisma.SubscriptionWhereInput[]
  OR?: Prisma.SubscriptionWhereInput[]
  NOT?: Prisma.SubscriptionWhereInput | Prisma.SubscriptionWhereInput[]
  plan?: Prisma.StringFilter<"Subscription"> | string
  referenceId?: Prisma.StringFilter<"Subscription"> | string
  stripeCustomerId?: Prisma.StringNullableFilter<"Subscription"> | string | null
  stripeSubscriptionId?: Prisma.StringNullableFilter<"Subscription"> | string | null
  status?: Prisma.StringFilter<"Subscription"> | string
  periodStart?: Prisma.DateTimeNullableFilter<"Subscription"> | Date | string | null
  periodEnd?: Prisma.DateTimeNullableFilter<"Subscription"> | Date | string | null
  cancelAtPeriodEnd?: Prisma.BoolNullableFilter<"Subscription"> | boolean | null
  seats?: Prisma.IntNullableFilter<"Subscription"> | number | null
  trialStart?: Prisma.DateTimeNullableFilter<"Subscription"> | Date | string | null
  trialEnd?: Prisma.DateTimeNullableFilter<"Subscription"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"Subscription"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Subscription"> | Date | string
}, "id">

export type SubscriptionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  plan?: Prisma.SortOrder
  referenceId?: Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrderInput | Prisma.SortOrder
  stripeSubscriptionId?: Prisma.SortOrderInput | Prisma.SortOrder
  status?: Prisma.SortOrder
  periodStart?: Prisma.SortOrderInput | Prisma.SortOrder
  periodEnd?: Prisma.SortOrderInput | Prisma.SortOrder
  cancelAtPeriodEnd?: Prisma.SortOrderInput | Prisma.SortOrder
  seats?: Prisma.SortOrderInput | Prisma.SortOrder
  trialStart?: Prisma.SortOrderInput | Prisma.SortOrder
  trialEnd?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.SubscriptionCountOrderByAggregateInput
  _avg?: Prisma.SubscriptionAvgOrderByAggregateInput
  _max?: Prisma.SubscriptionMaxOrderByAggregateInput
  _min?: Prisma.SubscriptionMinOrderByAggregateInput
  _sum?: Prisma.SubscriptionSumOrderByAggregateInput
}

export type SubscriptionScalarWhereWithAggregatesInput = {
  AND?: Prisma.SubscriptionScalarWhereWithAggregatesInput | Prisma.SubscriptionScalarWhereWithAggregatesInput[]
  OR?: Prisma.SubscriptionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.SubscriptionScalarWhereWithAggregatesInput | Prisma.SubscriptionScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Subscription"> | string
  plan?: Prisma.StringWithAggregatesFilter<"Subscription"> | string
  referenceId?: Prisma.StringWithAggregatesFilter<"Subscription"> | string
  stripeCustomerId?: Prisma.StringNullableWithAggregatesFilter<"Subscription"> | string | null
  stripeSubscriptionId?: Prisma.StringNullableWithAggregatesFilter<"Subscription"> | string | null
  status?: Prisma.StringWithAggregatesFilter<"Subscription"> | string
  periodStart?: Prisma.DateTimeNullableWithAggregatesFilter<"Subscription"> | Date | string | null
  periodEnd?: Prisma.DateTimeNullableWithAggregatesFilter<"Subscription"> | Date | string | null
  cancelAtPeriodEnd?: Prisma.BoolNullableWithAggregatesFilter<"Subscription"> | boolean | null
  seats?: Prisma.IntNullableWithAggregatesFilter<"Subscription"> | number | null
  trialStart?: Prisma.DateTimeNullableWithAggregatesFilter<"Subscription"> | Date | string | null
  trialEnd?: Prisma.DateTimeNullableWithAggregatesFilter<"Subscription"> | Date | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Subscription"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Subscription"> | Date | string
}

export type SubscriptionCreateInput = {
  id?: string
  plan: string
  referenceId: string
  stripeCustomerId?: string | null
  stripeSubscriptionId?: string | null
  status: string
  periodStart?: Date | string | null
  periodEnd?: Date | string | null
  cancelAtPeriodEnd?: boolean | null
  seats?: number | null
  trialStart?: Date | string | null
  trialEnd?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SubscriptionUncheckedCreateInput = {
  id?: string
  plan: string
  referenceId: string
  stripeCustomerId?: string | null
  stripeSubscriptionId?: string | null
  status: string
  periodStart?: Date | string | null
  periodEnd?: Date | string | null
  cancelAtPeriodEnd?: boolean | null
  seats?: number | null
  trialStart?: Date | string | null
  trialEnd?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SubscriptionUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  plan?: Prisma.StringFieldUpdateOperationsInput | string
  referenceId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeSubscriptionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  periodStart?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  periodEnd?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cancelAtPeriodEnd?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  seats?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  trialStart?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  trialEnd?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SubscriptionUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  plan?: Prisma.StringFieldUpdateOperationsInput | string
  referenceId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeSubscriptionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  periodStart?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  periodEnd?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cancelAtPeriodEnd?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  seats?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  trialStart?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  trialEnd?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SubscriptionCreateManyInput = {
  id?: string
  plan: string
  referenceId: string
  stripeCustomerId?: string | null
  stripeSubscriptionId?: string | null
  status: string
  periodStart?: Date | string | null
  periodEnd?: Date | string | null
  cancelAtPeriodEnd?: boolean | null
  seats?: number | null
  trialStart?: Date | string | null
  trialEnd?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type SubscriptionUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  plan?: Prisma.StringFieldUpdateOperationsInput | string
  referenceId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeSubscriptionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  periodStart?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  periodEnd?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cancelAtPeriodEnd?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  seats?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  trialStart?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  trialEnd?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SubscriptionUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  plan?: Prisma.StringFieldUpdateOperationsInput | string
  referenceId?: Prisma.StringFieldUpdateOperationsInput | string
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeSubscriptionId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  status?: Prisma.StringFieldUpdateOperationsInput | string
  periodStart?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  periodEnd?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  cancelAtPeriodEnd?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  seats?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  trialStart?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  trialEnd?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type SubscriptionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plan?: Prisma.SortOrder
  referenceId?: Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrder
  stripeSubscriptionId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  periodStart?: Prisma.SortOrder
  periodEnd?: Prisma.SortOrder
  cancelAtPeriodEnd?: Prisma.SortOrder
  seats?: Prisma.SortOrder
  trialStart?: Prisma.SortOrder
  trialEnd?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type SubscriptionAvgOrderByAggregateInput = {
  seats?: Prisma.SortOrder
}

export type SubscriptionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plan?: Prisma.SortOrder
  referenceId?: Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrder
  stripeSubscriptionId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  periodStart?: Prisma.SortOrder
  periodEnd?: Prisma.SortOrder
  cancelAtPeriodEnd?: Prisma.SortOrder
  seats?: Prisma.SortOrder
  trialStart?: Prisma.SortOrder
  trialEnd?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type SubscriptionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  plan?: Prisma.SortOrder
  referenceId?: Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrder
  stripeSubscriptionId?: Prisma.SortOrder
  status?: Prisma.SortOrder
  periodStart?: Prisma.SortOrder
  periodEnd?: Prisma.SortOrder
  cancelAtPeriodEnd?: Prisma.SortOrder
  seats?: Prisma.SortOrder
  trialStart?: Prisma.SortOrder
  trialEnd?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type SubscriptionSumOrderByAggregateInput = {
  seats?: Prisma.SortOrder
}

export type NullableIntFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}



export type SubscriptionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  plan?: boolean
  referenceId?: boolean
  stripeCustomerId?: boolean
  stripeSubscriptionId?: boolean
  status?: boolean
  periodStart?: boolean
  periodEnd?: boolean
  cancelAtPeriodEnd?: boolean
  seats?: boolean
  trialStart?: boolean
  trialEnd?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["subscription"]>

export type SubscriptionSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  plan?: boolean
  referenceId?: boolean
  stripeCustomerId?: boolean
  stripeSubscriptionId?: boolean
  status?: boolean
  periodStart?: boolean
  periodEnd?: boolean
  cancelAtPeriodEnd?: boolean
  seats?: boolean
  trialStart?: boolean
  trialEnd?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["subscription"]>

export type SubscriptionSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  plan?: boolean
  referenceId?: boolean
  stripeCustomerId?: boolean
  stripeSubscriptionId?: boolean
  status?: boolean
  periodStart?: boolean
  periodEnd?: boolean
  cancelAtPeriodEnd?: boolean
  seats?: boolean
  trialStart?: boolean
  trialEnd?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["subscription"]>

export type SubscriptionSelectScalar = {
  id?: boolean
  plan?: boolean
  referenceId?: boolean
  stripeCustomerId?: boolean
  stripeSubscriptionId?: boolean
  status?: boolean
  periodStart?: boolean
  periodEnd?: boolean
  cancelAtPeriodEnd?: boolean
  seats?: boolean
  trialStart?: boolean
  trialEnd?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type SubscriptionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "plan" | "referenceId" | "stripeCustomerId" | "stripeSubscriptionId" | "status" | "periodStart" | "periodEnd" | "cancelAtPeriodEnd" | "seats" | "trialStart" | "trialEnd" | "createdAt" | "updatedAt", ExtArgs["result"]["subscription"]>

export type $SubscriptionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Subscription"
  objects: {}
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    plan: string
    referenceId: string
    stripeCustomerId: string | null
    stripeSubscriptionId: string | null
    status: string
    periodStart: Date | null
    periodEnd: Date | null
    cancelAtPeriodEnd: boolean | null
    seats: number | null
    trialStart: Date | null
    trialEnd: Date | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["subscription"]>
  composites: {}
}

export type SubscriptionGetPayload<S extends boolean | null | undefined | SubscriptionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload, S>

export type SubscriptionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<SubscriptionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: SubscriptionCountAggregateInputType | true
  }

export interface SubscriptionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Subscription'], meta: { name: 'Subscription' } }
  /**
   * Find zero or one Subscription that matches the filter.
   * @param {SubscriptionFindUniqueArgs} args - Arguments to find a Subscription
   * @example
   * // Get one Subscription
   * const subscription = await prisma.subscription.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SubscriptionFindUniqueArgs>(args: Prisma.SelectSubset<T, SubscriptionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__SubscriptionClient<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Subscription that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SubscriptionFindUniqueOrThrowArgs} args - Arguments to find a Subscription
   * @example
   * // Get one Subscription
   * const subscription = await prisma.subscription.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SubscriptionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, SubscriptionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__SubscriptionClient<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Subscription that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubscriptionFindFirstArgs} args - Arguments to find a Subscription
   * @example
   * // Get one Subscription
   * const subscription = await prisma.subscription.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SubscriptionFindFirstArgs>(args?: Prisma.SelectSubset<T, SubscriptionFindFirstArgs<ExtArgs>>): Prisma.Prisma__SubscriptionClient<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Subscription that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubscriptionFindFirstOrThrowArgs} args - Arguments to find a Subscription
   * @example
   * // Get one Subscription
   * const subscription = await prisma.subscription.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SubscriptionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, SubscriptionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__SubscriptionClient<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Subscriptions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubscriptionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Subscriptions
   * const subscriptions = await prisma.subscription.findMany()
   * 
   * // Get first 10 Subscriptions
   * const subscriptions = await prisma.subscription.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const subscriptionWithIdOnly = await prisma.subscription.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends SubscriptionFindManyArgs>(args?: Prisma.SelectSubset<T, SubscriptionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Subscription.
   * @param {SubscriptionCreateArgs} args - Arguments to create a Subscription.
   * @example
   * // Create one Subscription
   * const Subscription = await prisma.subscription.create({
   *   data: {
   *     // ... data to create a Subscription
   *   }
   * })
   * 
   */
  create<T extends SubscriptionCreateArgs>(args: Prisma.SelectSubset<T, SubscriptionCreateArgs<ExtArgs>>): Prisma.Prisma__SubscriptionClient<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Subscriptions.
   * @param {SubscriptionCreateManyArgs} args - Arguments to create many Subscriptions.
   * @example
   * // Create many Subscriptions
   * const subscription = await prisma.subscription.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends SubscriptionCreateManyArgs>(args?: Prisma.SelectSubset<T, SubscriptionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Subscriptions and returns the data saved in the database.
   * @param {SubscriptionCreateManyAndReturnArgs} args - Arguments to create many Subscriptions.
   * @example
   * // Create many Subscriptions
   * const subscription = await prisma.subscription.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Subscriptions and only return the `id`
   * const subscriptionWithIdOnly = await prisma.subscription.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends SubscriptionCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, SubscriptionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Subscription.
   * @param {SubscriptionDeleteArgs} args - Arguments to delete one Subscription.
   * @example
   * // Delete one Subscription
   * const Subscription = await prisma.subscription.delete({
   *   where: {
   *     // ... filter to delete one Subscription
   *   }
   * })
   * 
   */
  delete<T extends SubscriptionDeleteArgs>(args: Prisma.SelectSubset<T, SubscriptionDeleteArgs<ExtArgs>>): Prisma.Prisma__SubscriptionClient<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Subscription.
   * @param {SubscriptionUpdateArgs} args - Arguments to update one Subscription.
   * @example
   * // Update one Subscription
   * const subscription = await prisma.subscription.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends SubscriptionUpdateArgs>(args: Prisma.SelectSubset<T, SubscriptionUpdateArgs<ExtArgs>>): Prisma.Prisma__SubscriptionClient<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Subscriptions.
   * @param {SubscriptionDeleteManyArgs} args - Arguments to filter Subscriptions to delete.
   * @example
   * // Delete a few Subscriptions
   * const { count } = await prisma.subscription.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends SubscriptionDeleteManyArgs>(args?: Prisma.SelectSubset<T, SubscriptionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Subscriptions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubscriptionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Subscriptions
   * const subscription = await prisma.subscription.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends SubscriptionUpdateManyArgs>(args: Prisma.SelectSubset<T, SubscriptionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Subscriptions and returns the data updated in the database.
   * @param {SubscriptionUpdateManyAndReturnArgs} args - Arguments to update many Subscriptions.
   * @example
   * // Update many Subscriptions
   * const subscription = await prisma.subscription.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Subscriptions and only return the `id`
   * const subscriptionWithIdOnly = await prisma.subscription.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends SubscriptionUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, SubscriptionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Subscription.
   * @param {SubscriptionUpsertArgs} args - Arguments to update or create a Subscription.
   * @example
   * // Update or create a Subscription
   * const subscription = await prisma.subscription.upsert({
   *   create: {
   *     // ... data to create a Subscription
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Subscription we want to update
   *   }
   * })
   */
  upsert<T extends SubscriptionUpsertArgs>(args: Prisma.SelectSubset<T, SubscriptionUpsertArgs<ExtArgs>>): Prisma.Prisma__SubscriptionClient<runtime.Types.Result.GetResult<Prisma.$SubscriptionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Subscriptions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubscriptionCountArgs} args - Arguments to filter Subscriptions to count.
   * @example
   * // Count the number of Subscriptions
   * const count = await prisma.subscription.count({
   *   where: {
   *     // ... the filter for the Subscriptions we want to count
   *   }
   * })
  **/
  count<T extends SubscriptionCountArgs>(
    args?: Prisma.Subset<T, SubscriptionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SubscriptionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Subscription.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubscriptionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends SubscriptionAggregateArgs>(args: Prisma.Subset<T, SubscriptionAggregateArgs>): Prisma.PrismaPromise<GetSubscriptionAggregateType<T>>

  /**
   * Group by Subscription.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SubscriptionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends SubscriptionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SubscriptionGroupByArgs['orderBy'] }
      : { orderBy?: SubscriptionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, SubscriptionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSubscriptionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Subscription model
 */
readonly fields: SubscriptionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Subscription.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SubscriptionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Subscription model
 */
export interface SubscriptionFieldRefs {
  readonly id: Prisma.FieldRef<"Subscription", 'String'>
  readonly plan: Prisma.FieldRef<"Subscription", 'String'>
  readonly referenceId: Prisma.FieldRef<"Subscription", 'String'>
  readonly stripeCustomerId: Prisma.FieldRef<"Subscription", 'String'>
  readonly stripeSubscriptionId: Prisma.FieldRef<"Subscription", 'String'>
  readonly status: Prisma.FieldRef<"Subscription", 'String'>
  readonly periodStart: Prisma.FieldRef<"Subscription", 'DateTime'>
  readonly periodEnd: Prisma.FieldRef<"Subscription", 'DateTime'>
  readonly cancelAtPeriodEnd: Prisma.FieldRef<"Subscription", 'Boolean'>
  readonly seats: Prisma.FieldRef<"Subscription", 'Int'>
  readonly trialStart: Prisma.FieldRef<"Subscription", 'DateTime'>
  readonly trialEnd: Prisma.FieldRef<"Subscription", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"Subscription", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Subscription", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Subscription findUnique
 */
export type SubscriptionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * Filter, which Subscription to fetch.
   */
  where: Prisma.SubscriptionWhereUniqueInput
}

/**
 * Subscription findUniqueOrThrow
 */
export type SubscriptionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * Filter, which Subscription to fetch.
   */
  where: Prisma.SubscriptionWhereUniqueInput
}

/**
 * Subscription findFirst
 */
export type SubscriptionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * Filter, which Subscription to fetch.
   */
  where?: Prisma.SubscriptionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Subscriptions to fetch.
   */
  orderBy?: Prisma.SubscriptionOrderByWithRelationInput | Prisma.SubscriptionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Subscriptions.
   */
  cursor?: Prisma.SubscriptionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Subscriptions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Subscriptions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Subscriptions.
   */
  distinct?: Prisma.SubscriptionScalarFieldEnum | Prisma.SubscriptionScalarFieldEnum[]
}

/**
 * Subscription findFirstOrThrow
 */
export type SubscriptionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * Filter, which Subscription to fetch.
   */
  where?: Prisma.SubscriptionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Subscriptions to fetch.
   */
  orderBy?: Prisma.SubscriptionOrderByWithRelationInput | Prisma.SubscriptionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Subscriptions.
   */
  cursor?: Prisma.SubscriptionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Subscriptions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Subscriptions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Subscriptions.
   */
  distinct?: Prisma.SubscriptionScalarFieldEnum | Prisma.SubscriptionScalarFieldEnum[]
}

/**
 * Subscription findMany
 */
export type SubscriptionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * Filter, which Subscriptions to fetch.
   */
  where?: Prisma.SubscriptionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Subscriptions to fetch.
   */
  orderBy?: Prisma.SubscriptionOrderByWithRelationInput | Prisma.SubscriptionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Subscriptions.
   */
  cursor?: Prisma.SubscriptionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Subscriptions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Subscriptions.
   */
  skip?: number
  distinct?: Prisma.SubscriptionScalarFieldEnum | Prisma.SubscriptionScalarFieldEnum[]
}

/**
 * Subscription create
 */
export type SubscriptionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * The data needed to create a Subscription.
   */
  data: Prisma.XOR<Prisma.SubscriptionCreateInput, Prisma.SubscriptionUncheckedCreateInput>
}

/**
 * Subscription createMany
 */
export type SubscriptionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Subscriptions.
   */
  data: Prisma.SubscriptionCreateManyInput | Prisma.SubscriptionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Subscription createManyAndReturn
 */
export type SubscriptionCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * The data used to create many Subscriptions.
   */
  data: Prisma.SubscriptionCreateManyInput | Prisma.SubscriptionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Subscription update
 */
export type SubscriptionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * The data needed to update a Subscription.
   */
  data: Prisma.XOR<Prisma.SubscriptionUpdateInput, Prisma.SubscriptionUncheckedUpdateInput>
  /**
   * Choose, which Subscription to update.
   */
  where: Prisma.SubscriptionWhereUniqueInput
}

/**
 * Subscription updateMany
 */
export type SubscriptionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Subscriptions.
   */
  data: Prisma.XOR<Prisma.SubscriptionUpdateManyMutationInput, Prisma.SubscriptionUncheckedUpdateManyInput>
  /**
   * Filter which Subscriptions to update
   */
  where?: Prisma.SubscriptionWhereInput
  /**
   * Limit how many Subscriptions to update.
   */
  limit?: number
}

/**
 * Subscription updateManyAndReturn
 */
export type SubscriptionUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * The data used to update Subscriptions.
   */
  data: Prisma.XOR<Prisma.SubscriptionUpdateManyMutationInput, Prisma.SubscriptionUncheckedUpdateManyInput>
  /**
   * Filter which Subscriptions to update
   */
  where?: Prisma.SubscriptionWhereInput
  /**
   * Limit how many Subscriptions to update.
   */
  limit?: number
}

/**
 * Subscription upsert
 */
export type SubscriptionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * The filter to search for the Subscription to update in case it exists.
   */
  where: Prisma.SubscriptionWhereUniqueInput
  /**
   * In case the Subscription found by the `where` argument doesn't exist, create a new Subscription with this data.
   */
  create: Prisma.XOR<Prisma.SubscriptionCreateInput, Prisma.SubscriptionUncheckedCreateInput>
  /**
   * In case the Subscription was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SubscriptionUpdateInput, Prisma.SubscriptionUncheckedUpdateInput>
}

/**
 * Subscription delete
 */
export type SubscriptionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
  /**
   * Filter which Subscription to delete.
   */
  where: Prisma.SubscriptionWhereUniqueInput
}

/**
 * Subscription deleteMany
 */
export type SubscriptionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Subscriptions to delete
   */
  where?: Prisma.SubscriptionWhereInput
  /**
   * Limit how many Subscriptions to delete.
   */
  limit?: number
}

/**
 * Subscription without action
 */
export type SubscriptionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Subscription
   */
  select?: Prisma.SubscriptionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Subscription
   */
  omit?: Prisma.SubscriptionOmit<ExtArgs> | null
}
