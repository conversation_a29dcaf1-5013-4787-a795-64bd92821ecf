
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Contact` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Contact
 * 
 */
export type ContactModel = runtime.Types.Result.DefaultSelection<Prisma.$ContactPayload>

export type AggregateContact = {
  _count: ContactCountAggregateOutputType | null
  _min: ContactMinAggregateOutputType | null
  _max: ContactMaxAggregateOutputType | null
}

export type ContactMinAggregateOutputType = {
  id: string | null
  address: string | null
  company: string | null
  email: string | null
  firstName: string | null
  lastName: string | null
  phone: string | null
  type: string | null
  userId: string | null
  website: string | null
  notes: string | null
  archived: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ContactMaxAggregateOutputType = {
  id: string | null
  address: string | null
  company: string | null
  email: string | null
  firstName: string | null
  lastName: string | null
  phone: string | null
  type: string | null
  userId: string | null
  website: string | null
  notes: string | null
  archived: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ContactCountAggregateOutputType = {
  id: number
  address: number
  company: number
  email: number
  firstName: number
  lastName: number
  phone: number
  type: number
  userId: number
  website: number
  notes: number
  archived: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ContactMinAggregateInputType = {
  id?: true
  address?: true
  company?: true
  email?: true
  firstName?: true
  lastName?: true
  phone?: true
  type?: true
  userId?: true
  website?: true
  notes?: true
  archived?: true
  createdAt?: true
  updatedAt?: true
}

export type ContactMaxAggregateInputType = {
  id?: true
  address?: true
  company?: true
  email?: true
  firstName?: true
  lastName?: true
  phone?: true
  type?: true
  userId?: true
  website?: true
  notes?: true
  archived?: true
  createdAt?: true
  updatedAt?: true
}

export type ContactCountAggregateInputType = {
  id?: true
  address?: true
  company?: true
  email?: true
  firstName?: true
  lastName?: true
  phone?: true
  type?: true
  userId?: true
  website?: true
  notes?: true
  archived?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ContactAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Contact to aggregate.
   */
  where?: Prisma.ContactWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Contacts to fetch.
   */
  orderBy?: Prisma.ContactOrderByWithRelationInput | Prisma.ContactOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ContactWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Contacts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Contacts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Contacts
  **/
  _count?: true | ContactCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ContactMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ContactMaxAggregateInputType
}

export type GetContactAggregateType<T extends ContactAggregateArgs> = {
      [P in keyof T & keyof AggregateContact]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateContact[P]>
    : Prisma.GetScalarType<T[P], AggregateContact[P]>
}




export type ContactGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ContactWhereInput
  orderBy?: Prisma.ContactOrderByWithAggregationInput | Prisma.ContactOrderByWithAggregationInput[]
  by: Prisma.ContactScalarFieldEnum[] | Prisma.ContactScalarFieldEnum
  having?: Prisma.ContactScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ContactCountAggregateInputType | true
  _min?: ContactMinAggregateInputType
  _max?: ContactMaxAggregateInputType
}

export type ContactGroupByOutputType = {
  id: string
  address: string
  company: string
  email: string
  firstName: string
  lastName: string
  phone: string
  type: string
  userId: string
  website: string
  notes: string
  archived: boolean
  createdAt: Date
  updatedAt: Date
  _count: ContactCountAggregateOutputType | null
  _min: ContactMinAggregateOutputType | null
  _max: ContactMaxAggregateOutputType | null
}

type GetContactGroupByPayload<T extends ContactGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ContactGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ContactGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ContactGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ContactGroupByOutputType[P]>
      }
    >
  > 



export type ContactWhereInput = {
  AND?: Prisma.ContactWhereInput | Prisma.ContactWhereInput[]
  OR?: Prisma.ContactWhereInput[]
  NOT?: Prisma.ContactWhereInput | Prisma.ContactWhereInput[]
  id?: Prisma.StringFilter<"Contact"> | string
  address?: Prisma.StringFilter<"Contact"> | string
  company?: Prisma.StringFilter<"Contact"> | string
  email?: Prisma.StringFilter<"Contact"> | string
  firstName?: Prisma.StringFilter<"Contact"> | string
  lastName?: Prisma.StringFilter<"Contact"> | string
  phone?: Prisma.StringFilter<"Contact"> | string
  type?: Prisma.StringFilter<"Contact"> | string
  userId?: Prisma.StringFilter<"Contact"> | string
  website?: Prisma.StringFilter<"Contact"> | string
  notes?: Prisma.StringFilter<"Contact"> | string
  archived?: Prisma.BoolFilter<"Contact"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Contact"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Contact"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  projects?: Prisma.ProjectListRelationFilter
  invoices?: Prisma.InvoiceListRelationFilter
}

export type ContactOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  address?: Prisma.SortOrder
  company?: Prisma.SortOrder
  email?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  type?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  website?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
  projects?: Prisma.ProjectOrderByRelationAggregateInput
  invoices?: Prisma.InvoiceOrderByRelationAggregateInput
}

export type ContactWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  email?: string
  AND?: Prisma.ContactWhereInput | Prisma.ContactWhereInput[]
  OR?: Prisma.ContactWhereInput[]
  NOT?: Prisma.ContactWhereInput | Prisma.ContactWhereInput[]
  address?: Prisma.StringFilter<"Contact"> | string
  company?: Prisma.StringFilter<"Contact"> | string
  firstName?: Prisma.StringFilter<"Contact"> | string
  lastName?: Prisma.StringFilter<"Contact"> | string
  phone?: Prisma.StringFilter<"Contact"> | string
  type?: Prisma.StringFilter<"Contact"> | string
  userId?: Prisma.StringFilter<"Contact"> | string
  website?: Prisma.StringFilter<"Contact"> | string
  notes?: Prisma.StringFilter<"Contact"> | string
  archived?: Prisma.BoolFilter<"Contact"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Contact"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Contact"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  projects?: Prisma.ProjectListRelationFilter
  invoices?: Prisma.InvoiceListRelationFilter
}, "id" | "email">

export type ContactOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  address?: Prisma.SortOrder
  company?: Prisma.SortOrder
  email?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  type?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  website?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ContactCountOrderByAggregateInput
  _max?: Prisma.ContactMaxOrderByAggregateInput
  _min?: Prisma.ContactMinOrderByAggregateInput
}

export type ContactScalarWhereWithAggregatesInput = {
  AND?: Prisma.ContactScalarWhereWithAggregatesInput | Prisma.ContactScalarWhereWithAggregatesInput[]
  OR?: Prisma.ContactScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ContactScalarWhereWithAggregatesInput | Prisma.ContactScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  address?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  company?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  email?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  firstName?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  lastName?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  phone?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  type?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  userId?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  website?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  notes?: Prisma.StringWithAggregatesFilter<"Contact"> | string
  archived?: Prisma.BoolWithAggregatesFilter<"Contact"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Contact"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Contact"> | Date | string
}

export type ContactCreateInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutContactsInput
  projects?: Prisma.ProjectCreateNestedManyWithoutContactInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutContactInput
}

export type ContactUncheckedCreateInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  userId: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutContactInput
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutContactInput
}

export type ContactUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutContactsNestedInput
  projects?: Prisma.ProjectUpdateManyWithoutContactNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutContactNestedInput
}

export type ContactUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutContactNestedInput
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutContactNestedInput
}

export type ContactCreateManyInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  userId: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ContactUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ContactUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ContactListRelationFilter = {
  every?: Prisma.ContactWhereInput
  some?: Prisma.ContactWhereInput
  none?: Prisma.ContactWhereInput
}

export type ContactOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ContactCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  address?: Prisma.SortOrder
  company?: Prisma.SortOrder
  email?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  type?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  website?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ContactMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  address?: Prisma.SortOrder
  company?: Prisma.SortOrder
  email?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  type?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  website?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ContactMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  address?: Prisma.SortOrder
  company?: Prisma.SortOrder
  email?: Prisma.SortOrder
  firstName?: Prisma.SortOrder
  lastName?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  type?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  website?: Prisma.SortOrder
  notes?: Prisma.SortOrder
  archived?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ContactNullableScalarRelationFilter = {
  is?: Prisma.ContactWhereInput | null
  isNot?: Prisma.ContactWhereInput | null
}

export type ContactCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ContactCreateWithoutUserInput, Prisma.ContactUncheckedCreateWithoutUserInput> | Prisma.ContactCreateWithoutUserInput[] | Prisma.ContactUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ContactCreateOrConnectWithoutUserInput | Prisma.ContactCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ContactCreateManyUserInputEnvelope
  connect?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
}

export type ContactUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ContactCreateWithoutUserInput, Prisma.ContactUncheckedCreateWithoutUserInput> | Prisma.ContactCreateWithoutUserInput[] | Prisma.ContactUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ContactCreateOrConnectWithoutUserInput | Prisma.ContactCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ContactCreateManyUserInputEnvelope
  connect?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
}

export type ContactUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ContactCreateWithoutUserInput, Prisma.ContactUncheckedCreateWithoutUserInput> | Prisma.ContactCreateWithoutUserInput[] | Prisma.ContactUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ContactCreateOrConnectWithoutUserInput | Prisma.ContactCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ContactUpsertWithWhereUniqueWithoutUserInput | Prisma.ContactUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ContactCreateManyUserInputEnvelope
  set?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
  disconnect?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
  delete?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
  connect?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
  update?: Prisma.ContactUpdateWithWhereUniqueWithoutUserInput | Prisma.ContactUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ContactUpdateManyWithWhereWithoutUserInput | Prisma.ContactUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ContactScalarWhereInput | Prisma.ContactScalarWhereInput[]
}

export type ContactUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ContactCreateWithoutUserInput, Prisma.ContactUncheckedCreateWithoutUserInput> | Prisma.ContactCreateWithoutUserInput[] | Prisma.ContactUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ContactCreateOrConnectWithoutUserInput | Prisma.ContactCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ContactUpsertWithWhereUniqueWithoutUserInput | Prisma.ContactUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ContactCreateManyUserInputEnvelope
  set?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
  disconnect?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
  delete?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
  connect?: Prisma.ContactWhereUniqueInput | Prisma.ContactWhereUniqueInput[]
  update?: Prisma.ContactUpdateWithWhereUniqueWithoutUserInput | Prisma.ContactUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ContactUpdateManyWithWhereWithoutUserInput | Prisma.ContactUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ContactScalarWhereInput | Prisma.ContactScalarWhereInput[]
}

export type ContactCreateNestedOneWithoutProjectsInput = {
  create?: Prisma.XOR<Prisma.ContactCreateWithoutProjectsInput, Prisma.ContactUncheckedCreateWithoutProjectsInput>
  connectOrCreate?: Prisma.ContactCreateOrConnectWithoutProjectsInput
  connect?: Prisma.ContactWhereUniqueInput
}

export type ContactUpdateOneWithoutProjectsNestedInput = {
  create?: Prisma.XOR<Prisma.ContactCreateWithoutProjectsInput, Prisma.ContactUncheckedCreateWithoutProjectsInput>
  connectOrCreate?: Prisma.ContactCreateOrConnectWithoutProjectsInput
  upsert?: Prisma.ContactUpsertWithoutProjectsInput
  disconnect?: Prisma.ContactWhereInput | boolean
  delete?: Prisma.ContactWhereInput | boolean
  connect?: Prisma.ContactWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ContactUpdateToOneWithWhereWithoutProjectsInput, Prisma.ContactUpdateWithoutProjectsInput>, Prisma.ContactUncheckedUpdateWithoutProjectsInput>
}

export type ContactCreateNestedOneWithoutInvoicesInput = {
  create?: Prisma.XOR<Prisma.ContactCreateWithoutInvoicesInput, Prisma.ContactUncheckedCreateWithoutInvoicesInput>
  connectOrCreate?: Prisma.ContactCreateOrConnectWithoutInvoicesInput
  connect?: Prisma.ContactWhereUniqueInput
}

export type ContactUpdateOneWithoutInvoicesNestedInput = {
  create?: Prisma.XOR<Prisma.ContactCreateWithoutInvoicesInput, Prisma.ContactUncheckedCreateWithoutInvoicesInput>
  connectOrCreate?: Prisma.ContactCreateOrConnectWithoutInvoicesInput
  upsert?: Prisma.ContactUpsertWithoutInvoicesInput
  disconnect?: Prisma.ContactWhereInput | boolean
  delete?: Prisma.ContactWhereInput | boolean
  connect?: Prisma.ContactWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ContactUpdateToOneWithWhereWithoutInvoicesInput, Prisma.ContactUpdateWithoutInvoicesInput>, Prisma.ContactUncheckedUpdateWithoutInvoicesInput>
}

export type ContactCreateWithoutUserInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  projects?: Prisma.ProjectCreateNestedManyWithoutContactInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutContactInput
}

export type ContactUncheckedCreateWithoutUserInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutContactInput
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutContactInput
}

export type ContactCreateOrConnectWithoutUserInput = {
  where: Prisma.ContactWhereUniqueInput
  create: Prisma.XOR<Prisma.ContactCreateWithoutUserInput, Prisma.ContactUncheckedCreateWithoutUserInput>
}

export type ContactCreateManyUserInputEnvelope = {
  data: Prisma.ContactCreateManyUserInput | Prisma.ContactCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type ContactUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.ContactWhereUniqueInput
  update: Prisma.XOR<Prisma.ContactUpdateWithoutUserInput, Prisma.ContactUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.ContactCreateWithoutUserInput, Prisma.ContactUncheckedCreateWithoutUserInput>
}

export type ContactUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.ContactWhereUniqueInput
  data: Prisma.XOR<Prisma.ContactUpdateWithoutUserInput, Prisma.ContactUncheckedUpdateWithoutUserInput>
}

export type ContactUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.ContactScalarWhereInput
  data: Prisma.XOR<Prisma.ContactUpdateManyMutationInput, Prisma.ContactUncheckedUpdateManyWithoutUserInput>
}

export type ContactScalarWhereInput = {
  AND?: Prisma.ContactScalarWhereInput | Prisma.ContactScalarWhereInput[]
  OR?: Prisma.ContactScalarWhereInput[]
  NOT?: Prisma.ContactScalarWhereInput | Prisma.ContactScalarWhereInput[]
  id?: Prisma.StringFilter<"Contact"> | string
  address?: Prisma.StringFilter<"Contact"> | string
  company?: Prisma.StringFilter<"Contact"> | string
  email?: Prisma.StringFilter<"Contact"> | string
  firstName?: Prisma.StringFilter<"Contact"> | string
  lastName?: Prisma.StringFilter<"Contact"> | string
  phone?: Prisma.StringFilter<"Contact"> | string
  type?: Prisma.StringFilter<"Contact"> | string
  userId?: Prisma.StringFilter<"Contact"> | string
  website?: Prisma.StringFilter<"Contact"> | string
  notes?: Prisma.StringFilter<"Contact"> | string
  archived?: Prisma.BoolFilter<"Contact"> | boolean
  createdAt?: Prisma.DateTimeFilter<"Contact"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Contact"> | Date | string
}

export type ContactCreateWithoutProjectsInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutContactsInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutContactInput
}

export type ContactUncheckedCreateWithoutProjectsInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  userId: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutContactInput
}

export type ContactCreateOrConnectWithoutProjectsInput = {
  where: Prisma.ContactWhereUniqueInput
  create: Prisma.XOR<Prisma.ContactCreateWithoutProjectsInput, Prisma.ContactUncheckedCreateWithoutProjectsInput>
}

export type ContactUpsertWithoutProjectsInput = {
  update: Prisma.XOR<Prisma.ContactUpdateWithoutProjectsInput, Prisma.ContactUncheckedUpdateWithoutProjectsInput>
  create: Prisma.XOR<Prisma.ContactCreateWithoutProjectsInput, Prisma.ContactUncheckedCreateWithoutProjectsInput>
  where?: Prisma.ContactWhereInput
}

export type ContactUpdateToOneWithWhereWithoutProjectsInput = {
  where?: Prisma.ContactWhereInput
  data: Prisma.XOR<Prisma.ContactUpdateWithoutProjectsInput, Prisma.ContactUncheckedUpdateWithoutProjectsInput>
}

export type ContactUpdateWithoutProjectsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutContactsNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutContactNestedInput
}

export type ContactUncheckedUpdateWithoutProjectsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutContactNestedInput
}

export type ContactCreateWithoutInvoicesInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutContactsInput
  projects?: Prisma.ProjectCreateNestedManyWithoutContactInput
}

export type ContactUncheckedCreateWithoutInvoicesInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  userId: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutContactInput
}

export type ContactCreateOrConnectWithoutInvoicesInput = {
  where: Prisma.ContactWhereUniqueInput
  create: Prisma.XOR<Prisma.ContactCreateWithoutInvoicesInput, Prisma.ContactUncheckedCreateWithoutInvoicesInput>
}

export type ContactUpsertWithoutInvoicesInput = {
  update: Prisma.XOR<Prisma.ContactUpdateWithoutInvoicesInput, Prisma.ContactUncheckedUpdateWithoutInvoicesInput>
  create: Prisma.XOR<Prisma.ContactCreateWithoutInvoicesInput, Prisma.ContactUncheckedCreateWithoutInvoicesInput>
  where?: Prisma.ContactWhereInput
}

export type ContactUpdateToOneWithWhereWithoutInvoicesInput = {
  where?: Prisma.ContactWhereInput
  data: Prisma.XOR<Prisma.ContactUpdateWithoutInvoicesInput, Prisma.ContactUncheckedUpdateWithoutInvoicesInput>
}

export type ContactUpdateWithoutInvoicesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutContactsNestedInput
  projects?: Prisma.ProjectUpdateManyWithoutContactNestedInput
}

export type ContactUncheckedUpdateWithoutInvoicesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutContactNestedInput
}

export type ContactCreateManyUserInput = {
  id?: string
  address?: string
  company?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  type?: string
  website?: string
  notes?: string
  archived?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ContactUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  projects?: Prisma.ProjectUpdateManyWithoutContactNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutContactNestedInput
}

export type ContactUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutContactNestedInput
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutContactNestedInput
}

export type ContactUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  address?: Prisma.StringFieldUpdateOperationsInput | string
  company?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  firstName?: Prisma.StringFieldUpdateOperationsInput | string
  lastName?: Prisma.StringFieldUpdateOperationsInput | string
  phone?: Prisma.StringFieldUpdateOperationsInput | string
  type?: Prisma.StringFieldUpdateOperationsInput | string
  website?: Prisma.StringFieldUpdateOperationsInput | string
  notes?: Prisma.StringFieldUpdateOperationsInput | string
  archived?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type ContactCountOutputType
 */

export type ContactCountOutputType = {
  projects: number
  invoices: number
}

export type ContactCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  projects?: boolean | ContactCountOutputTypeCountProjectsArgs
  invoices?: boolean | ContactCountOutputTypeCountInvoicesArgs
}

/**
 * ContactCountOutputType without action
 */
export type ContactCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ContactCountOutputType
   */
  select?: Prisma.ContactCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ContactCountOutputType without action
 */
export type ContactCountOutputTypeCountProjectsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ProjectWhereInput
}

/**
 * ContactCountOutputType without action
 */
export type ContactCountOutputTypeCountInvoicesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.InvoiceWhereInput
}


export type ContactSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  address?: boolean
  company?: boolean
  email?: boolean
  firstName?: boolean
  lastName?: boolean
  phone?: boolean
  type?: boolean
  userId?: boolean
  website?: boolean
  notes?: boolean
  archived?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  projects?: boolean | Prisma.Contact$projectsArgs<ExtArgs>
  invoices?: boolean | Prisma.Contact$invoicesArgs<ExtArgs>
  _count?: boolean | Prisma.ContactCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["contact"]>

export type ContactSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  address?: boolean
  company?: boolean
  email?: boolean
  firstName?: boolean
  lastName?: boolean
  phone?: boolean
  type?: boolean
  userId?: boolean
  website?: boolean
  notes?: boolean
  archived?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["contact"]>

export type ContactSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  address?: boolean
  company?: boolean
  email?: boolean
  firstName?: boolean
  lastName?: boolean
  phone?: boolean
  type?: boolean
  userId?: boolean
  website?: boolean
  notes?: boolean
  archived?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["contact"]>

export type ContactSelectScalar = {
  id?: boolean
  address?: boolean
  company?: boolean
  email?: boolean
  firstName?: boolean
  lastName?: boolean
  phone?: boolean
  type?: boolean
  userId?: boolean
  website?: boolean
  notes?: boolean
  archived?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ContactOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "address" | "company" | "email" | "firstName" | "lastName" | "phone" | "type" | "userId" | "website" | "notes" | "archived" | "createdAt" | "updatedAt", ExtArgs["result"]["contact"]>
export type ContactInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  projects?: boolean | Prisma.Contact$projectsArgs<ExtArgs>
  invoices?: boolean | Prisma.Contact$invoicesArgs<ExtArgs>
  _count?: boolean | Prisma.ContactCountOutputTypeDefaultArgs<ExtArgs>
}
export type ContactIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type ContactIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $ContactPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Contact"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
    projects: Prisma.$ProjectPayload<ExtArgs>[]
    invoices: Prisma.$InvoicePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    address: string
    company: string
    email: string
    firstName: string
    lastName: string
    phone: string
    type: string
    userId: string
    website: string
    notes: string
    archived: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["contact"]>
  composites: {}
}

export type ContactGetPayload<S extends boolean | null | undefined | ContactDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ContactPayload, S>

export type ContactCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ContactFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ContactCountAggregateInputType | true
  }

export interface ContactDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Contact'], meta: { name: 'Contact' } }
  /**
   * Find zero or one Contact that matches the filter.
   * @param {ContactFindUniqueArgs} args - Arguments to find a Contact
   * @example
   * // Get one Contact
   * const contact = await prisma.contact.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ContactFindUniqueArgs>(args: Prisma.SelectSubset<T, ContactFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Contact that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ContactFindUniqueOrThrowArgs} args - Arguments to find a Contact
   * @example
   * // Get one Contact
   * const contact = await prisma.contact.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ContactFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ContactFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Contact that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContactFindFirstArgs} args - Arguments to find a Contact
   * @example
   * // Get one Contact
   * const contact = await prisma.contact.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ContactFindFirstArgs>(args?: Prisma.SelectSubset<T, ContactFindFirstArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Contact that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContactFindFirstOrThrowArgs} args - Arguments to find a Contact
   * @example
   * // Get one Contact
   * const contact = await prisma.contact.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ContactFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ContactFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Contacts that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContactFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Contacts
   * const contacts = await prisma.contact.findMany()
   * 
   * // Get first 10 Contacts
   * const contacts = await prisma.contact.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const contactWithIdOnly = await prisma.contact.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ContactFindManyArgs>(args?: Prisma.SelectSubset<T, ContactFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Contact.
   * @param {ContactCreateArgs} args - Arguments to create a Contact.
   * @example
   * // Create one Contact
   * const Contact = await prisma.contact.create({
   *   data: {
   *     // ... data to create a Contact
   *   }
   * })
   * 
   */
  create<T extends ContactCreateArgs>(args: Prisma.SelectSubset<T, ContactCreateArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Contacts.
   * @param {ContactCreateManyArgs} args - Arguments to create many Contacts.
   * @example
   * // Create many Contacts
   * const contact = await prisma.contact.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ContactCreateManyArgs>(args?: Prisma.SelectSubset<T, ContactCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Contacts and returns the data saved in the database.
   * @param {ContactCreateManyAndReturnArgs} args - Arguments to create many Contacts.
   * @example
   * // Create many Contacts
   * const contact = await prisma.contact.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Contacts and only return the `id`
   * const contactWithIdOnly = await prisma.contact.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ContactCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ContactCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Contact.
   * @param {ContactDeleteArgs} args - Arguments to delete one Contact.
   * @example
   * // Delete one Contact
   * const Contact = await prisma.contact.delete({
   *   where: {
   *     // ... filter to delete one Contact
   *   }
   * })
   * 
   */
  delete<T extends ContactDeleteArgs>(args: Prisma.SelectSubset<T, ContactDeleteArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Contact.
   * @param {ContactUpdateArgs} args - Arguments to update one Contact.
   * @example
   * // Update one Contact
   * const contact = await prisma.contact.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ContactUpdateArgs>(args: Prisma.SelectSubset<T, ContactUpdateArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Contacts.
   * @param {ContactDeleteManyArgs} args - Arguments to filter Contacts to delete.
   * @example
   * // Delete a few Contacts
   * const { count } = await prisma.contact.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ContactDeleteManyArgs>(args?: Prisma.SelectSubset<T, ContactDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Contacts.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContactUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Contacts
   * const contact = await prisma.contact.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ContactUpdateManyArgs>(args: Prisma.SelectSubset<T, ContactUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Contacts and returns the data updated in the database.
   * @param {ContactUpdateManyAndReturnArgs} args - Arguments to update many Contacts.
   * @example
   * // Update many Contacts
   * const contact = await prisma.contact.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Contacts and only return the `id`
   * const contactWithIdOnly = await prisma.contact.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ContactUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ContactUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Contact.
   * @param {ContactUpsertArgs} args - Arguments to update or create a Contact.
   * @example
   * // Update or create a Contact
   * const contact = await prisma.contact.upsert({
   *   create: {
   *     // ... data to create a Contact
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Contact we want to update
   *   }
   * })
   */
  upsert<T extends ContactUpsertArgs>(args: Prisma.SelectSubset<T, ContactUpsertArgs<ExtArgs>>): Prisma.Prisma__ContactClient<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Contacts.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContactCountArgs} args - Arguments to filter Contacts to count.
   * @example
   * // Count the number of Contacts
   * const count = await prisma.contact.count({
   *   where: {
   *     // ... the filter for the Contacts we want to count
   *   }
   * })
  **/
  count<T extends ContactCountArgs>(
    args?: Prisma.Subset<T, ContactCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ContactCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Contact.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContactAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ContactAggregateArgs>(args: Prisma.Subset<T, ContactAggregateArgs>): Prisma.PrismaPromise<GetContactAggregateType<T>>

  /**
   * Group by Contact.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContactGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ContactGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ContactGroupByArgs['orderBy'] }
      : { orderBy?: ContactGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ContactGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetContactGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Contact model
 */
readonly fields: ContactFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Contact.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ContactClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  projects<T extends Prisma.Contact$projectsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Contact$projectsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  invoices<T extends Prisma.Contact$invoicesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Contact$invoicesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Contact model
 */
export interface ContactFieldRefs {
  readonly id: Prisma.FieldRef<"Contact", 'String'>
  readonly address: Prisma.FieldRef<"Contact", 'String'>
  readonly company: Prisma.FieldRef<"Contact", 'String'>
  readonly email: Prisma.FieldRef<"Contact", 'String'>
  readonly firstName: Prisma.FieldRef<"Contact", 'String'>
  readonly lastName: Prisma.FieldRef<"Contact", 'String'>
  readonly phone: Prisma.FieldRef<"Contact", 'String'>
  readonly type: Prisma.FieldRef<"Contact", 'String'>
  readonly userId: Prisma.FieldRef<"Contact", 'String'>
  readonly website: Prisma.FieldRef<"Contact", 'String'>
  readonly notes: Prisma.FieldRef<"Contact", 'String'>
  readonly archived: Prisma.FieldRef<"Contact", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"Contact", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Contact", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Contact findUnique
 */
export type ContactFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * Filter, which Contact to fetch.
   */
  where: Prisma.ContactWhereUniqueInput
}

/**
 * Contact findUniqueOrThrow
 */
export type ContactFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * Filter, which Contact to fetch.
   */
  where: Prisma.ContactWhereUniqueInput
}

/**
 * Contact findFirst
 */
export type ContactFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * Filter, which Contact to fetch.
   */
  where?: Prisma.ContactWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Contacts to fetch.
   */
  orderBy?: Prisma.ContactOrderByWithRelationInput | Prisma.ContactOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Contacts.
   */
  cursor?: Prisma.ContactWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Contacts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Contacts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Contacts.
   */
  distinct?: Prisma.ContactScalarFieldEnum | Prisma.ContactScalarFieldEnum[]
}

/**
 * Contact findFirstOrThrow
 */
export type ContactFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * Filter, which Contact to fetch.
   */
  where?: Prisma.ContactWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Contacts to fetch.
   */
  orderBy?: Prisma.ContactOrderByWithRelationInput | Prisma.ContactOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Contacts.
   */
  cursor?: Prisma.ContactWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Contacts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Contacts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Contacts.
   */
  distinct?: Prisma.ContactScalarFieldEnum | Prisma.ContactScalarFieldEnum[]
}

/**
 * Contact findMany
 */
export type ContactFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * Filter, which Contacts to fetch.
   */
  where?: Prisma.ContactWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Contacts to fetch.
   */
  orderBy?: Prisma.ContactOrderByWithRelationInput | Prisma.ContactOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Contacts.
   */
  cursor?: Prisma.ContactWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Contacts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Contacts.
   */
  skip?: number
  distinct?: Prisma.ContactScalarFieldEnum | Prisma.ContactScalarFieldEnum[]
}

/**
 * Contact create
 */
export type ContactCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * The data needed to create a Contact.
   */
  data: Prisma.XOR<Prisma.ContactCreateInput, Prisma.ContactUncheckedCreateInput>
}

/**
 * Contact createMany
 */
export type ContactCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Contacts.
   */
  data: Prisma.ContactCreateManyInput | Prisma.ContactCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Contact createManyAndReturn
 */
export type ContactCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * The data used to create many Contacts.
   */
  data: Prisma.ContactCreateManyInput | Prisma.ContactCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Contact update
 */
export type ContactUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * The data needed to update a Contact.
   */
  data: Prisma.XOR<Prisma.ContactUpdateInput, Prisma.ContactUncheckedUpdateInput>
  /**
   * Choose, which Contact to update.
   */
  where: Prisma.ContactWhereUniqueInput
}

/**
 * Contact updateMany
 */
export type ContactUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Contacts.
   */
  data: Prisma.XOR<Prisma.ContactUpdateManyMutationInput, Prisma.ContactUncheckedUpdateManyInput>
  /**
   * Filter which Contacts to update
   */
  where?: Prisma.ContactWhereInput
  /**
   * Limit how many Contacts to update.
   */
  limit?: number
}

/**
 * Contact updateManyAndReturn
 */
export type ContactUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * The data used to update Contacts.
   */
  data: Prisma.XOR<Prisma.ContactUpdateManyMutationInput, Prisma.ContactUncheckedUpdateManyInput>
  /**
   * Filter which Contacts to update
   */
  where?: Prisma.ContactWhereInput
  /**
   * Limit how many Contacts to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Contact upsert
 */
export type ContactUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * The filter to search for the Contact to update in case it exists.
   */
  where: Prisma.ContactWhereUniqueInput
  /**
   * In case the Contact found by the `where` argument doesn't exist, create a new Contact with this data.
   */
  create: Prisma.XOR<Prisma.ContactCreateInput, Prisma.ContactUncheckedCreateInput>
  /**
   * In case the Contact was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ContactUpdateInput, Prisma.ContactUncheckedUpdateInput>
}

/**
 * Contact delete
 */
export type ContactDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  /**
   * Filter which Contact to delete.
   */
  where: Prisma.ContactWhereUniqueInput
}

/**
 * Contact deleteMany
 */
export type ContactDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Contacts to delete
   */
  where?: Prisma.ContactWhereInput
  /**
   * Limit how many Contacts to delete.
   */
  limit?: number
}

/**
 * Contact.projects
 */
export type Contact$projectsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  where?: Prisma.ProjectWhereInput
  orderBy?: Prisma.ProjectOrderByWithRelationInput | Prisma.ProjectOrderByWithRelationInput[]
  cursor?: Prisma.ProjectWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ProjectScalarFieldEnum | Prisma.ProjectScalarFieldEnum[]
}

/**
 * Contact.invoices
 */
export type Contact$invoicesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  where?: Prisma.InvoiceWhereInput
  orderBy?: Prisma.InvoiceOrderByWithRelationInput | Prisma.InvoiceOrderByWithRelationInput[]
  cursor?: Prisma.InvoiceWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.InvoiceScalarFieldEnum | Prisma.InvoiceScalarFieldEnum[]
}

/**
 * Contact without action
 */
export type ContactDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
}
