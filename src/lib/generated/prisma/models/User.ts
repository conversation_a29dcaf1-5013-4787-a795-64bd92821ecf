
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `User` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model User
 * 
 */
export type UserModel = runtime.Types.Result.DefaultSelection<Prisma.$UserPayload>

export type AggregateUser = {
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

export type UserMinAggregateOutputType = {
  id: string | null
  name: string | null
  email: string | null
  emailVerified: boolean | null
  image: string | null
  role: string | null
  banned: boolean | null
  banReason: string | null
  banExpires: Date | null
  businessName: string | null
  jobTitle: string | null
  logo: string | null
  website: string | null
  phone: string | null
  address: string | null
  currency: string | null
  timezone: string | null
  stripeCustomerId: string | null
  stripeAccountId: string | null
  isStripeConnected: boolean | null
  isStripeUpToDate: boolean | null
  isStripeEnabled: boolean | null
  paypalAccountId: string | null
  isPaypalConnected: boolean | null
  isPaypalEnabled: boolean | null
  cashappAccountId: string | null
  isCashappConnected: boolean | null
  isCashappEnabled: boolean | null
  zelleAccountId: string | null
  isZelleConnected: boolean | null
  isZelleEnabled: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UserMaxAggregateOutputType = {
  id: string | null
  name: string | null
  email: string | null
  emailVerified: boolean | null
  image: string | null
  role: string | null
  banned: boolean | null
  banReason: string | null
  banExpires: Date | null
  businessName: string | null
  jobTitle: string | null
  logo: string | null
  website: string | null
  phone: string | null
  address: string | null
  currency: string | null
  timezone: string | null
  stripeCustomerId: string | null
  stripeAccountId: string | null
  isStripeConnected: boolean | null
  isStripeUpToDate: boolean | null
  isStripeEnabled: boolean | null
  paypalAccountId: string | null
  isPaypalConnected: boolean | null
  isPaypalEnabled: boolean | null
  cashappAccountId: string | null
  isCashappConnected: boolean | null
  isCashappEnabled: boolean | null
  zelleAccountId: string | null
  isZelleConnected: boolean | null
  isZelleEnabled: boolean | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UserCountAggregateOutputType = {
  id: number
  name: number
  email: number
  emailVerified: number
  image: number
  role: number
  banned: number
  banReason: number
  banExpires: number
  businessName: number
  jobTitle: number
  logo: number
  website: number
  phone: number
  address: number
  currency: number
  timezone: number
  stripeCustomerId: number
  stripeAccountId: number
  isStripeConnected: number
  isStripeUpToDate: number
  isStripeEnabled: number
  paypalAccountId: number
  isPaypalConnected: number
  isPaypalEnabled: number
  cashappAccountId: number
  isCashappConnected: number
  isCashappEnabled: number
  zelleAccountId: number
  isZelleConnected: number
  isZelleEnabled: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type UserMinAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  role?: true
  banned?: true
  banReason?: true
  banExpires?: true
  businessName?: true
  jobTitle?: true
  logo?: true
  website?: true
  phone?: true
  address?: true
  currency?: true
  timezone?: true
  stripeCustomerId?: true
  stripeAccountId?: true
  isStripeConnected?: true
  isStripeUpToDate?: true
  isStripeEnabled?: true
  paypalAccountId?: true
  isPaypalConnected?: true
  isPaypalEnabled?: true
  cashappAccountId?: true
  isCashappConnected?: true
  isCashappEnabled?: true
  zelleAccountId?: true
  isZelleConnected?: true
  isZelleEnabled?: true
  createdAt?: true
  updatedAt?: true
}

export type UserMaxAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  role?: true
  banned?: true
  banReason?: true
  banExpires?: true
  businessName?: true
  jobTitle?: true
  logo?: true
  website?: true
  phone?: true
  address?: true
  currency?: true
  timezone?: true
  stripeCustomerId?: true
  stripeAccountId?: true
  isStripeConnected?: true
  isStripeUpToDate?: true
  isStripeEnabled?: true
  paypalAccountId?: true
  isPaypalConnected?: true
  isPaypalEnabled?: true
  cashappAccountId?: true
  isCashappConnected?: true
  isCashappEnabled?: true
  zelleAccountId?: true
  isZelleConnected?: true
  isZelleEnabled?: true
  createdAt?: true
  updatedAt?: true
}

export type UserCountAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  role?: true
  banned?: true
  banReason?: true
  banExpires?: true
  businessName?: true
  jobTitle?: true
  logo?: true
  website?: true
  phone?: true
  address?: true
  currency?: true
  timezone?: true
  stripeCustomerId?: true
  stripeAccountId?: true
  isStripeConnected?: true
  isStripeUpToDate?: true
  isStripeEnabled?: true
  paypalAccountId?: true
  isPaypalConnected?: true
  isPaypalEnabled?: true
  cashappAccountId?: true
  isCashappConnected?: true
  isCashappEnabled?: true
  zelleAccountId?: true
  isZelleConnected?: true
  isZelleEnabled?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type UserAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which User to aggregate.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Users
  **/
  _count?: true | UserCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: UserMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: UserMaxAggregateInputType
}

export type GetUserAggregateType<T extends UserAggregateArgs> = {
      [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser[P]>
    : Prisma.GetScalarType<T[P], AggregateUser[P]>
}




export type UserGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UserWhereInput
  orderBy?: Prisma.UserOrderByWithAggregationInput | Prisma.UserOrderByWithAggregationInput[]
  by: Prisma.UserScalarFieldEnum[] | Prisma.UserScalarFieldEnum
  having?: Prisma.UserScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UserCountAggregateInputType | true
  _min?: UserMinAggregateInputType
  _max?: UserMaxAggregateInputType
}

export type UserGroupByOutputType = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image: string | null
  role: string | null
  banned: boolean | null
  banReason: string | null
  banExpires: Date | null
  businessName: string | null
  jobTitle: string | null
  logo: string | null
  website: string | null
  phone: string | null
  address: string | null
  currency: string | null
  timezone: string | null
  stripeCustomerId: string | null
  stripeAccountId: string | null
  isStripeConnected: boolean
  isStripeUpToDate: boolean
  isStripeEnabled: boolean
  paypalAccountId: string | null
  isPaypalConnected: boolean
  isPaypalEnabled: boolean
  cashappAccountId: string | null
  isCashappConnected: boolean
  isCashappEnabled: boolean
  zelleAccountId: string | null
  isZelleConnected: boolean
  isZelleEnabled: boolean
  createdAt: Date
  updatedAt: Date
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UserGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
      }
    >
  > 



export type UserWhereInput = {
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  id?: Prisma.StringFilter<"User"> | string
  name?: Prisma.StringFilter<"User"> | string
  email?: Prisma.StringFilter<"User"> | string
  emailVerified?: Prisma.BoolFilter<"User"> | boolean
  image?: Prisma.StringNullableFilter<"User"> | string | null
  role?: Prisma.StringNullableFilter<"User"> | string | null
  banned?: Prisma.BoolNullableFilter<"User"> | boolean | null
  banReason?: Prisma.StringNullableFilter<"User"> | string | null
  banExpires?: Prisma.DateTimeNullableFilter<"User"> | Date | string | null
  businessName?: Prisma.StringNullableFilter<"User"> | string | null
  jobTitle?: Prisma.StringNullableFilter<"User"> | string | null
  logo?: Prisma.StringNullableFilter<"User"> | string | null
  website?: Prisma.StringNullableFilter<"User"> | string | null
  phone?: Prisma.StringNullableFilter<"User"> | string | null
  address?: Prisma.StringNullableFilter<"User"> | string | null
  currency?: Prisma.StringNullableFilter<"User"> | string | null
  timezone?: Prisma.StringNullableFilter<"User"> | string | null
  stripeCustomerId?: Prisma.StringNullableFilter<"User"> | string | null
  stripeAccountId?: Prisma.StringNullableFilter<"User"> | string | null
  isStripeConnected?: Prisma.BoolFilter<"User"> | boolean
  isStripeUpToDate?: Prisma.BoolFilter<"User"> | boolean
  isStripeEnabled?: Prisma.BoolFilter<"User"> | boolean
  paypalAccountId?: Prisma.StringNullableFilter<"User"> | string | null
  isPaypalConnected?: Prisma.BoolFilter<"User"> | boolean
  isPaypalEnabled?: Prisma.BoolFilter<"User"> | boolean
  cashappAccountId?: Prisma.StringNullableFilter<"User"> | string | null
  isCashappConnected?: Prisma.BoolFilter<"User"> | boolean
  isCashappEnabled?: Prisma.BoolFilter<"User"> | boolean
  zelleAccountId?: Prisma.StringNullableFilter<"User"> | string | null
  isZelleConnected?: Prisma.BoolFilter<"User"> | boolean
  isZelleEnabled?: Prisma.BoolFilter<"User"> | boolean
  createdAt?: Prisma.DateTimeFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"User"> | Date | string
  sessions?: Prisma.SessionListRelationFilter
  accounts?: Prisma.AccountListRelationFilter
  passkeys?: Prisma.PasskeyListRelationFilter
  contacts?: Prisma.ContactListRelationFilter
  projects?: Prisma.ProjectListRelationFilter
  invoices?: Prisma.InvoiceListRelationFilter
}

export type UserOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  role?: Prisma.SortOrderInput | Prisma.SortOrder
  banned?: Prisma.SortOrderInput | Prisma.SortOrder
  banReason?: Prisma.SortOrderInput | Prisma.SortOrder
  banExpires?: Prisma.SortOrderInput | Prisma.SortOrder
  businessName?: Prisma.SortOrderInput | Prisma.SortOrder
  jobTitle?: Prisma.SortOrderInput | Prisma.SortOrder
  logo?: Prisma.SortOrderInput | Prisma.SortOrder
  website?: Prisma.SortOrderInput | Prisma.SortOrder
  phone?: Prisma.SortOrderInput | Prisma.SortOrder
  address?: Prisma.SortOrderInput | Prisma.SortOrder
  currency?: Prisma.SortOrderInput | Prisma.SortOrder
  timezone?: Prisma.SortOrderInput | Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrderInput | Prisma.SortOrder
  stripeAccountId?: Prisma.SortOrderInput | Prisma.SortOrder
  isStripeConnected?: Prisma.SortOrder
  isStripeUpToDate?: Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  paypalAccountId?: Prisma.SortOrderInput | Prisma.SortOrder
  isPaypalConnected?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  cashappAccountId?: Prisma.SortOrderInput | Prisma.SortOrder
  isCashappConnected?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  zelleAccountId?: Prisma.SortOrderInput | Prisma.SortOrder
  isZelleConnected?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  sessions?: Prisma.SessionOrderByRelationAggregateInput
  accounts?: Prisma.AccountOrderByRelationAggregateInput
  passkeys?: Prisma.PasskeyOrderByRelationAggregateInput
  contacts?: Prisma.ContactOrderByRelationAggregateInput
  projects?: Prisma.ProjectOrderByRelationAggregateInput
  invoices?: Prisma.InvoiceOrderByRelationAggregateInput
}

export type UserWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  email?: string
  stripeAccountId?: string
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  name?: Prisma.StringFilter<"User"> | string
  emailVerified?: Prisma.BoolFilter<"User"> | boolean
  image?: Prisma.StringNullableFilter<"User"> | string | null
  role?: Prisma.StringNullableFilter<"User"> | string | null
  banned?: Prisma.BoolNullableFilter<"User"> | boolean | null
  banReason?: Prisma.StringNullableFilter<"User"> | string | null
  banExpires?: Prisma.DateTimeNullableFilter<"User"> | Date | string | null
  businessName?: Prisma.StringNullableFilter<"User"> | string | null
  jobTitle?: Prisma.StringNullableFilter<"User"> | string | null
  logo?: Prisma.StringNullableFilter<"User"> | string | null
  website?: Prisma.StringNullableFilter<"User"> | string | null
  phone?: Prisma.StringNullableFilter<"User"> | string | null
  address?: Prisma.StringNullableFilter<"User"> | string | null
  currency?: Prisma.StringNullableFilter<"User"> | string | null
  timezone?: Prisma.StringNullableFilter<"User"> | string | null
  stripeCustomerId?: Prisma.StringNullableFilter<"User"> | string | null
  isStripeConnected?: Prisma.BoolFilter<"User"> | boolean
  isStripeUpToDate?: Prisma.BoolFilter<"User"> | boolean
  isStripeEnabled?: Prisma.BoolFilter<"User"> | boolean
  paypalAccountId?: Prisma.StringNullableFilter<"User"> | string | null
  isPaypalConnected?: Prisma.BoolFilter<"User"> | boolean
  isPaypalEnabled?: Prisma.BoolFilter<"User"> | boolean
  cashappAccountId?: Prisma.StringNullableFilter<"User"> | string | null
  isCashappConnected?: Prisma.BoolFilter<"User"> | boolean
  isCashappEnabled?: Prisma.BoolFilter<"User"> | boolean
  zelleAccountId?: Prisma.StringNullableFilter<"User"> | string | null
  isZelleConnected?: Prisma.BoolFilter<"User"> | boolean
  isZelleEnabled?: Prisma.BoolFilter<"User"> | boolean
  createdAt?: Prisma.DateTimeFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"User"> | Date | string
  sessions?: Prisma.SessionListRelationFilter
  accounts?: Prisma.AccountListRelationFilter
  passkeys?: Prisma.PasskeyListRelationFilter
  contacts?: Prisma.ContactListRelationFilter
  projects?: Prisma.ProjectListRelationFilter
  invoices?: Prisma.InvoiceListRelationFilter
}, "id" | "stripeAccountId" | "email">

export type UserOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  role?: Prisma.SortOrderInput | Prisma.SortOrder
  banned?: Prisma.SortOrderInput | Prisma.SortOrder
  banReason?: Prisma.SortOrderInput | Prisma.SortOrder
  banExpires?: Prisma.SortOrderInput | Prisma.SortOrder
  businessName?: Prisma.SortOrderInput | Prisma.SortOrder
  jobTitle?: Prisma.SortOrderInput | Prisma.SortOrder
  logo?: Prisma.SortOrderInput | Prisma.SortOrder
  website?: Prisma.SortOrderInput | Prisma.SortOrder
  phone?: Prisma.SortOrderInput | Prisma.SortOrder
  address?: Prisma.SortOrderInput | Prisma.SortOrder
  currency?: Prisma.SortOrderInput | Prisma.SortOrder
  timezone?: Prisma.SortOrderInput | Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrderInput | Prisma.SortOrder
  stripeAccountId?: Prisma.SortOrderInput | Prisma.SortOrder
  isStripeConnected?: Prisma.SortOrder
  isStripeUpToDate?: Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  paypalAccountId?: Prisma.SortOrderInput | Prisma.SortOrder
  isPaypalConnected?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  cashappAccountId?: Prisma.SortOrderInput | Prisma.SortOrder
  isCashappConnected?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  zelleAccountId?: Prisma.SortOrderInput | Prisma.SortOrder
  isZelleConnected?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.UserCountOrderByAggregateInput
  _max?: Prisma.UserMaxOrderByAggregateInput
  _min?: Prisma.UserMinOrderByAggregateInput
}

export type UserScalarWhereWithAggregatesInput = {
  AND?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  OR?: Prisma.UserScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"User"> | string
  name?: Prisma.StringWithAggregatesFilter<"User"> | string
  email?: Prisma.StringWithAggregatesFilter<"User"> | string
  emailVerified?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  image?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  role?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  banned?: Prisma.BoolNullableWithAggregatesFilter<"User"> | boolean | null
  banReason?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  banExpires?: Prisma.DateTimeNullableWithAggregatesFilter<"User"> | Date | string | null
  businessName?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  jobTitle?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  logo?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  website?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  phone?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  address?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  currency?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  timezone?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  stripeCustomerId?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  stripeAccountId?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  isStripeConnected?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  isStripeUpToDate?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  isStripeEnabled?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  paypalAccountId?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  isPaypalConnected?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  isPaypalEnabled?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  cashappAccountId?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  isCashappConnected?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  isCashappEnabled?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  zelleAccountId?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  isZelleConnected?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  isZelleEnabled?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"User"> | Date | string
}

export type UserCreateInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactUncheckedCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutUserInput
}

export type UserUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUncheckedUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateManyInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type UserUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UserUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UserCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  role?: Prisma.SortOrder
  banned?: Prisma.SortOrder
  banReason?: Prisma.SortOrder
  banExpires?: Prisma.SortOrder
  businessName?: Prisma.SortOrder
  jobTitle?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  website?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  address?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  timezone?: Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrder
  stripeAccountId?: Prisma.SortOrder
  isStripeConnected?: Prisma.SortOrder
  isStripeUpToDate?: Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  paypalAccountId?: Prisma.SortOrder
  isPaypalConnected?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  cashappAccountId?: Prisma.SortOrder
  isCashappConnected?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  zelleAccountId?: Prisma.SortOrder
  isZelleConnected?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  role?: Prisma.SortOrder
  banned?: Prisma.SortOrder
  banReason?: Prisma.SortOrder
  banExpires?: Prisma.SortOrder
  businessName?: Prisma.SortOrder
  jobTitle?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  website?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  address?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  timezone?: Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrder
  stripeAccountId?: Prisma.SortOrder
  isStripeConnected?: Prisma.SortOrder
  isStripeUpToDate?: Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  paypalAccountId?: Prisma.SortOrder
  isPaypalConnected?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  cashappAccountId?: Prisma.SortOrder
  isCashappConnected?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  zelleAccountId?: Prisma.SortOrder
  isZelleConnected?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  role?: Prisma.SortOrder
  banned?: Prisma.SortOrder
  banReason?: Prisma.SortOrder
  banExpires?: Prisma.SortOrder
  businessName?: Prisma.SortOrder
  jobTitle?: Prisma.SortOrder
  logo?: Prisma.SortOrder
  website?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  address?: Prisma.SortOrder
  currency?: Prisma.SortOrder
  timezone?: Prisma.SortOrder
  stripeCustomerId?: Prisma.SortOrder
  stripeAccountId?: Prisma.SortOrder
  isStripeConnected?: Prisma.SortOrder
  isStripeUpToDate?: Prisma.SortOrder
  isStripeEnabled?: Prisma.SortOrder
  paypalAccountId?: Prisma.SortOrder
  isPaypalConnected?: Prisma.SortOrder
  isPaypalEnabled?: Prisma.SortOrder
  cashappAccountId?: Prisma.SortOrder
  isCashappConnected?: Prisma.SortOrder
  isCashappEnabled?: Prisma.SortOrder
  zelleAccountId?: Prisma.SortOrder
  isZelleConnected?: Prisma.SortOrder
  isZelleEnabled?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserScalarRelationFilter = {
  is?: Prisma.UserWhereInput
  isNot?: Prisma.UserWhereInput
}

export type StringFieldUpdateOperationsInput = {
  set?: string
}

export type BoolFieldUpdateOperationsInput = {
  set?: boolean
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type NullableBoolFieldUpdateOperationsInput = {
  set?: boolean | null
}

export type NullableDateTimeFieldUpdateOperationsInput = {
  set?: Date | string | null
}

export type DateTimeFieldUpdateOperationsInput = {
  set?: Date | string
}

export type UserCreateNestedOneWithoutSessionsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSessionsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutSessionsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSessionsInput
  upsert?: Prisma.UserUpsertWithoutSessionsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutSessionsInput, Prisma.UserUpdateWithoutSessionsInput>, Prisma.UserUncheckedUpdateWithoutSessionsInput>
}

export type UserCreateNestedOneWithoutAccountsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAccountsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutAccountsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAccountsInput
  upsert?: Prisma.UserUpsertWithoutAccountsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutAccountsInput, Prisma.UserUpdateWithoutAccountsInput>, Prisma.UserUncheckedUpdateWithoutAccountsInput>
}

export type UserCreateNestedOneWithoutPasskeysInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutPasskeysInput, Prisma.UserUncheckedCreateWithoutPasskeysInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutPasskeysInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutPasskeysNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutPasskeysInput, Prisma.UserUncheckedCreateWithoutPasskeysInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutPasskeysInput
  upsert?: Prisma.UserUpsertWithoutPasskeysInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutPasskeysInput, Prisma.UserUpdateWithoutPasskeysInput>, Prisma.UserUncheckedUpdateWithoutPasskeysInput>
}

export type UserCreateNestedOneWithoutContactsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutContactsInput, Prisma.UserUncheckedCreateWithoutContactsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutContactsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutContactsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutContactsInput, Prisma.UserUncheckedCreateWithoutContactsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutContactsInput
  upsert?: Prisma.UserUpsertWithoutContactsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutContactsInput, Prisma.UserUpdateWithoutContactsInput>, Prisma.UserUncheckedUpdateWithoutContactsInput>
}

export type UserCreateNestedOneWithoutProjectsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutProjectsInput, Prisma.UserUncheckedCreateWithoutProjectsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutProjectsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutProjectsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutProjectsInput, Prisma.UserUncheckedCreateWithoutProjectsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutProjectsInput
  upsert?: Prisma.UserUpsertWithoutProjectsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutProjectsInput, Prisma.UserUpdateWithoutProjectsInput>, Prisma.UserUncheckedUpdateWithoutProjectsInput>
}

export type UserCreateNestedOneWithoutInvoicesInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutInvoicesInput, Prisma.UserUncheckedCreateWithoutInvoicesInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutInvoicesInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutInvoicesNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutInvoicesInput, Prisma.UserUncheckedCreateWithoutInvoicesInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutInvoicesInput
  upsert?: Prisma.UserUpsertWithoutInvoicesInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutInvoicesInput, Prisma.UserUpdateWithoutInvoicesInput>, Prisma.UserUncheckedUpdateWithoutInvoicesInput>
}

export type UserCreateWithoutSessionsInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutSessionsInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactUncheckedCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutSessionsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
}

export type UserUpsertWithoutSessionsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutSessionsInput, Prisma.UserUncheckedUpdateWithoutSessionsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutSessionsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutSessionsInput, Prisma.UserUncheckedUpdateWithoutSessionsInput>
}

export type UserUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUncheckedUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutAccountsInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutAccountsInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactUncheckedCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutAccountsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
}

export type UserUpsertWithoutAccountsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutAccountsInput, Prisma.UserUncheckedUpdateWithoutAccountsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAccountsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutAccountsInput, Prisma.UserUncheckedUpdateWithoutAccountsInput>
}

export type UserUpdateWithoutAccountsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutAccountsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUncheckedUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutPasskeysInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutPasskeysInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactUncheckedCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutPasskeysInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutPasskeysInput, Prisma.UserUncheckedCreateWithoutPasskeysInput>
}

export type UserUpsertWithoutPasskeysInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutPasskeysInput, Prisma.UserUncheckedUpdateWithoutPasskeysInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutPasskeysInput, Prisma.UserUncheckedCreateWithoutPasskeysInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutPasskeysInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutPasskeysInput, Prisma.UserUncheckedUpdateWithoutPasskeysInput>
}

export type UserUpdateWithoutPasskeysInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutPasskeysInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUncheckedUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutContactsInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutContactsInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutContactsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutContactsInput, Prisma.UserUncheckedCreateWithoutContactsInput>
}

export type UserUpsertWithoutContactsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutContactsInput, Prisma.UserUncheckedUpdateWithoutContactsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutContactsInput, Prisma.UserUncheckedCreateWithoutContactsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutContactsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutContactsInput, Prisma.UserUncheckedUpdateWithoutContactsInput>
}

export type UserUpdateWithoutContactsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutContactsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutProjectsInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutProjectsInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactUncheckedCreateNestedManyWithoutUserInput
  invoices?: Prisma.InvoiceUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutProjectsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutProjectsInput, Prisma.UserUncheckedCreateWithoutProjectsInput>
}

export type UserUpsertWithoutProjectsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutProjectsInput, Prisma.UserUncheckedUpdateWithoutProjectsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutProjectsInput, Prisma.UserUncheckedCreateWithoutProjectsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutProjectsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutProjectsInput, Prisma.UserUncheckedUpdateWithoutProjectsInput>
}

export type UserUpdateWithoutProjectsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutProjectsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUncheckedUpdateManyWithoutUserNestedInput
  invoices?: Prisma.InvoiceUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutInvoicesInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutInvoicesInput = {
  id?: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  businessName?: string | null
  jobTitle?: string | null
  logo?: string | null
  website?: string | null
  phone?: string | null
  address?: string | null
  currency?: string | null
  timezone?: string | null
  stripeCustomerId?: string | null
  stripeAccountId?: string | null
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: string | null
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: string | null
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: string | null
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: Date | string
  updatedAt?: Date | string
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
  passkeys?: Prisma.PasskeyUncheckedCreateNestedManyWithoutUserInput
  contacts?: Prisma.ContactUncheckedCreateNestedManyWithoutUserInput
  projects?: Prisma.ProjectUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutInvoicesInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutInvoicesInput, Prisma.UserUncheckedCreateWithoutInvoicesInput>
}

export type UserUpsertWithoutInvoicesInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutInvoicesInput, Prisma.UserUncheckedUpdateWithoutInvoicesInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutInvoicesInput, Prisma.UserUncheckedCreateWithoutInvoicesInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutInvoicesInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutInvoicesInput, Prisma.UserUncheckedUpdateWithoutInvoicesInput>
}

export type UserUpdateWithoutInvoicesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutInvoicesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  businessName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  jobTitle?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  logo?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  website?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  address?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  currency?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  timezone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeCustomerId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  stripeAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isStripeConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeUpToDate?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isStripeEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  paypalAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isPaypalConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isPaypalEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  cashappAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isCashappConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isCashappEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  zelleAccountId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  isZelleConnected?: Prisma.BoolFieldUpdateOperationsInput | boolean
  isZelleEnabled?: Prisma.BoolFieldUpdateOperationsInput | boolean
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
  passkeys?: Prisma.PasskeyUncheckedUpdateManyWithoutUserNestedInput
  contacts?: Prisma.ContactUncheckedUpdateManyWithoutUserNestedInput
  projects?: Prisma.ProjectUncheckedUpdateManyWithoutUserNestedInput
}


/**
 * Count Type UserCountOutputType
 */

export type UserCountOutputType = {
  sessions: number
  accounts: number
  passkeys: number
  contacts: number
  projects: number
  invoices: number
}

export type UserCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sessions?: boolean | UserCountOutputTypeCountSessionsArgs
  accounts?: boolean | UserCountOutputTypeCountAccountsArgs
  passkeys?: boolean | UserCountOutputTypeCountPasskeysArgs
  contacts?: boolean | UserCountOutputTypeCountContactsArgs
  projects?: boolean | UserCountOutputTypeCountProjectsArgs
  invoices?: boolean | UserCountOutputTypeCountInvoicesArgs
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserCountOutputType
   */
  select?: Prisma.UserCountOutputTypeSelect<ExtArgs> | null
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountSessionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SessionWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountAccountsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AccountWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountPasskeysArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PasskeyWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountContactsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ContactWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountProjectsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ProjectWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountInvoicesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.InvoiceWhereInput
}


export type UserSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  role?: boolean
  banned?: boolean
  banReason?: boolean
  banExpires?: boolean
  businessName?: boolean
  jobTitle?: boolean
  logo?: boolean
  website?: boolean
  phone?: boolean
  address?: boolean
  currency?: boolean
  timezone?: boolean
  stripeCustomerId?: boolean
  stripeAccountId?: boolean
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: boolean
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: boolean
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: boolean
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  sessions?: boolean | Prisma.User$sessionsArgs<ExtArgs>
  accounts?: boolean | Prisma.User$accountsArgs<ExtArgs>
  passkeys?: boolean | Prisma.User$passkeysArgs<ExtArgs>
  contacts?: boolean | Prisma.User$contactsArgs<ExtArgs>
  projects?: boolean | Prisma.User$projectsArgs<ExtArgs>
  invoices?: boolean | Prisma.User$invoicesArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user"]>

export type UserSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  role?: boolean
  banned?: boolean
  banReason?: boolean
  banExpires?: boolean
  businessName?: boolean
  jobTitle?: boolean
  logo?: boolean
  website?: boolean
  phone?: boolean
  address?: boolean
  currency?: boolean
  timezone?: boolean
  stripeCustomerId?: boolean
  stripeAccountId?: boolean
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: boolean
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: boolean
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: boolean
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["user"]>

export type UserSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  role?: boolean
  banned?: boolean
  banReason?: boolean
  banExpires?: boolean
  businessName?: boolean
  jobTitle?: boolean
  logo?: boolean
  website?: boolean
  phone?: boolean
  address?: boolean
  currency?: boolean
  timezone?: boolean
  stripeCustomerId?: boolean
  stripeAccountId?: boolean
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: boolean
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: boolean
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: boolean
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["user"]>

export type UserSelectScalar = {
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  role?: boolean
  banned?: boolean
  banReason?: boolean
  banExpires?: boolean
  businessName?: boolean
  jobTitle?: boolean
  logo?: boolean
  website?: boolean
  phone?: boolean
  address?: boolean
  currency?: boolean
  timezone?: boolean
  stripeCustomerId?: boolean
  stripeAccountId?: boolean
  isStripeConnected?: boolean
  isStripeUpToDate?: boolean
  isStripeEnabled?: boolean
  paypalAccountId?: boolean
  isPaypalConnected?: boolean
  isPaypalEnabled?: boolean
  cashappAccountId?: boolean
  isCashappConnected?: boolean
  isCashappEnabled?: boolean
  zelleAccountId?: boolean
  isZelleConnected?: boolean
  isZelleEnabled?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type UserOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "email" | "emailVerified" | "image" | "role" | "banned" | "banReason" | "banExpires" | "businessName" | "jobTitle" | "logo" | "website" | "phone" | "address" | "currency" | "timezone" | "stripeCustomerId" | "stripeAccountId" | "isStripeConnected" | "isStripeUpToDate" | "isStripeEnabled" | "paypalAccountId" | "isPaypalConnected" | "isPaypalEnabled" | "cashappAccountId" | "isCashappConnected" | "isCashappEnabled" | "zelleAccountId" | "isZelleConnected" | "isZelleEnabled" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
export type UserInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  sessions?: boolean | Prisma.User$sessionsArgs<ExtArgs>
  accounts?: boolean | Prisma.User$accountsArgs<ExtArgs>
  passkeys?: boolean | Prisma.User$passkeysArgs<ExtArgs>
  contacts?: boolean | Prisma.User$contactsArgs<ExtArgs>
  projects?: boolean | Prisma.User$projectsArgs<ExtArgs>
  invoices?: boolean | Prisma.User$invoicesArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}
export type UserIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}
export type UserIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}

export type $UserPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "User"
  objects: {
    sessions: Prisma.$SessionPayload<ExtArgs>[]
    accounts: Prisma.$AccountPayload<ExtArgs>[]
    passkeys: Prisma.$PasskeyPayload<ExtArgs>[]
    contacts: Prisma.$ContactPayload<ExtArgs>[]
    projects: Prisma.$ProjectPayload<ExtArgs>[]
    invoices: Prisma.$InvoicePayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    email: string
    emailVerified: boolean
    image: string | null
    role: string | null
    banned: boolean | null
    banReason: string | null
    banExpires: Date | null
    businessName: string | null
    jobTitle: string | null
    logo: string | null
    website: string | null
    phone: string | null
    address: string | null
    currency: string | null
    timezone: string | null
    stripeCustomerId: string | null
    stripeAccountId: string | null
    isStripeConnected: boolean
    isStripeUpToDate: boolean
    isStripeEnabled: boolean
    paypalAccountId: string | null
    isPaypalConnected: boolean
    isPaypalEnabled: boolean
    cashappAccountId: string | null
    isCashappConnected: boolean
    isCashappEnabled: boolean
    zelleAccountId: string | null
    isZelleConnected: boolean
    isZelleEnabled: boolean
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["user"]>
  composites: {}
}

export type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$UserPayload, S>

export type UserCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: UserCountAggregateInputType | true
  }

export interface UserDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
  /**
   * Find zero or one User that matches the filter.
   * @param {UserFindUniqueArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UserFindUniqueArgs>(args: Prisma.SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UserFindFirstArgs>(args?: Prisma.SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Users that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Users
   * const users = await prisma.user.findMany()
   * 
   * // Get first 10 Users
   * const users = await prisma.user.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends UserFindManyArgs>(args?: Prisma.SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User.
   * @param {UserCreateArgs} args - Arguments to create a User.
   * @example
   * // Create one User
   * const User = await prisma.user.create({
   *   data: {
   *     // ... data to create a User
   *   }
   * })
   * 
   */
  create<T extends UserCreateArgs>(args: Prisma.SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Users.
   * @param {UserCreateManyArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends UserCreateManyArgs>(args?: Prisma.SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Users and returns the data saved in the database.
   * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Users and only return the `id`
   * const userWithIdOnly = await prisma.user.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a User.
   * @param {UserDeleteArgs} args - Arguments to delete one User.
   * @example
   * // Delete one User
   * const User = await prisma.user.delete({
   *   where: {
   *     // ... filter to delete one User
   *   }
   * })
   * 
   */
  delete<T extends UserDeleteArgs>(args: Prisma.SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User.
   * @param {UserUpdateArgs} args - Arguments to update one User.
   * @example
   * // Update one User
   * const user = await prisma.user.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends UserUpdateArgs>(args: Prisma.SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Users.
   * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
   * @example
   * // Delete a few Users
   * const { count } = await prisma.user.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends UserDeleteManyArgs>(args?: Prisma.SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends UserUpdateManyArgs>(args: Prisma.SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users and returns the data updated in the database.
   * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Users and only return the `id`
   * const userWithIdOnly = await prisma.user.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one User.
   * @param {UserUpsertArgs} args - Arguments to update or create a User.
   * @example
   * // Update or create a User
   * const user = await prisma.user.upsert({
   *   create: {
   *     // ... data to create a User
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User we want to update
   *   }
   * })
   */
  upsert<T extends UserUpsertArgs>(args: Prisma.SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserCountArgs} args - Arguments to filter Users to count.
   * @example
   * // Count the number of Users
   * const count = await prisma.user.count({
   *   where: {
   *     // ... the filter for the Users we want to count
   *   }
   * })
  **/
  count<T extends UserCountArgs>(
    args?: Prisma.Subset<T, UserCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UserCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends UserAggregateArgs>(args: Prisma.Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

  /**
   * Group by User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends UserGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UserGroupByArgs['orderBy'] }
      : { orderBy?: UserGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the User model
 */
readonly fields: UserFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for User.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UserClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  sessions<T extends Prisma.User$sessionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$sessionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  accounts<T extends Prisma.User$accountsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$accountsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AccountPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  passkeys<T extends Prisma.User$passkeysArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$passkeysArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  contacts<T extends Prisma.User$contactsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$contactsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ContactPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  projects<T extends Prisma.User$projectsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$projectsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ProjectPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  invoices<T extends Prisma.User$invoicesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$invoicesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$InvoicePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the User model
 */
export interface UserFieldRefs {
  readonly id: Prisma.FieldRef<"User", 'String'>
  readonly name: Prisma.FieldRef<"User", 'String'>
  readonly email: Prisma.FieldRef<"User", 'String'>
  readonly emailVerified: Prisma.FieldRef<"User", 'Boolean'>
  readonly image: Prisma.FieldRef<"User", 'String'>
  readonly role: Prisma.FieldRef<"User", 'String'>
  readonly banned: Prisma.FieldRef<"User", 'Boolean'>
  readonly banReason: Prisma.FieldRef<"User", 'String'>
  readonly banExpires: Prisma.FieldRef<"User", 'DateTime'>
  readonly businessName: Prisma.FieldRef<"User", 'String'>
  readonly jobTitle: Prisma.FieldRef<"User", 'String'>
  readonly logo: Prisma.FieldRef<"User", 'String'>
  readonly website: Prisma.FieldRef<"User", 'String'>
  readonly phone: Prisma.FieldRef<"User", 'String'>
  readonly address: Prisma.FieldRef<"User", 'String'>
  readonly currency: Prisma.FieldRef<"User", 'String'>
  readonly timezone: Prisma.FieldRef<"User", 'String'>
  readonly stripeCustomerId: Prisma.FieldRef<"User", 'String'>
  readonly stripeAccountId: Prisma.FieldRef<"User", 'String'>
  readonly isStripeConnected: Prisma.FieldRef<"User", 'Boolean'>
  readonly isStripeUpToDate: Prisma.FieldRef<"User", 'Boolean'>
  readonly isStripeEnabled: Prisma.FieldRef<"User", 'Boolean'>
  readonly paypalAccountId: Prisma.FieldRef<"User", 'String'>
  readonly isPaypalConnected: Prisma.FieldRef<"User", 'Boolean'>
  readonly isPaypalEnabled: Prisma.FieldRef<"User", 'Boolean'>
  readonly cashappAccountId: Prisma.FieldRef<"User", 'String'>
  readonly isCashappConnected: Prisma.FieldRef<"User", 'Boolean'>
  readonly isCashappEnabled: Prisma.FieldRef<"User", 'Boolean'>
  readonly zelleAccountId: Prisma.FieldRef<"User", 'String'>
  readonly isZelleConnected: Prisma.FieldRef<"User", 'Boolean'>
  readonly isZelleEnabled: Prisma.FieldRef<"User", 'Boolean'>
  readonly createdAt: Prisma.FieldRef<"User", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"User", 'DateTime'>
}
    

// Custom InputTypes
/**
 * User findUnique
 */
export type UserFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findUniqueOrThrow
 */
export type UserFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findFirst
 */
export type UserFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findFirstOrThrow
 */
export type UserFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findMany
 */
export type UserFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which Users to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User create
 */
export type UserCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to create a User.
   */
  data: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
}

/**
 * User createMany
 */
export type UserCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * User createManyAndReturn
 */
export type UserCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * User update
 */
export type UserUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to update a User.
   */
  data: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
  /**
   * Choose, which User to update.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User updateMany
 */
export type UserUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
}

/**
 * User updateManyAndReturn
 */
export type UserUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
}

/**
 * User upsert
 */
export type UserUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The filter to search for the User to update in case it exists.
   */
  where: Prisma.UserWhereUniqueInput
  /**
   * In case the User found by the `where` argument doesn't exist, create a new User with this data.
   */
  create: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
  /**
   * In case the User was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
}

/**
 * User delete
 */
export type UserDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter which User to delete.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User deleteMany
 */
export type UserDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Users to delete
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to delete.
   */
  limit?: number
}

/**
 * User.sessions
 */
export type User$sessionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  where?: Prisma.SessionWhereInput
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  cursor?: Prisma.SessionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * User.accounts
 */
export type User$accountsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Account
   */
  select?: Prisma.AccountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Account
   */
  omit?: Prisma.AccountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AccountInclude<ExtArgs> | null
  where?: Prisma.AccountWhereInput
  orderBy?: Prisma.AccountOrderByWithRelationInput | Prisma.AccountOrderByWithRelationInput[]
  cursor?: Prisma.AccountWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AccountScalarFieldEnum | Prisma.AccountScalarFieldEnum[]
}

/**
 * User.passkeys
 */
export type User$passkeysArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  where?: Prisma.PasskeyWhereInput
  orderBy?: Prisma.PasskeyOrderByWithRelationInput | Prisma.PasskeyOrderByWithRelationInput[]
  cursor?: Prisma.PasskeyWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.PasskeyScalarFieldEnum | Prisma.PasskeyScalarFieldEnum[]
}

/**
 * User.contacts
 */
export type User$contactsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contact
   */
  select?: Prisma.ContactSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contact
   */
  omit?: Prisma.ContactOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContactInclude<ExtArgs> | null
  where?: Prisma.ContactWhereInput
  orderBy?: Prisma.ContactOrderByWithRelationInput | Prisma.ContactOrderByWithRelationInput[]
  cursor?: Prisma.ContactWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ContactScalarFieldEnum | Prisma.ContactScalarFieldEnum[]
}

/**
 * User.projects
 */
export type User$projectsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Project
   */
  select?: Prisma.ProjectSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Project
   */
  omit?: Prisma.ProjectOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ProjectInclude<ExtArgs> | null
  where?: Prisma.ProjectWhereInput
  orderBy?: Prisma.ProjectOrderByWithRelationInput | Prisma.ProjectOrderByWithRelationInput[]
  cursor?: Prisma.ProjectWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ProjectScalarFieldEnum | Prisma.ProjectScalarFieldEnum[]
}

/**
 * User.invoices
 */
export type User$invoicesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Invoice
   */
  select?: Prisma.InvoiceSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Invoice
   */
  omit?: Prisma.InvoiceOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.InvoiceInclude<ExtArgs> | null
  where?: Prisma.InvoiceWhereInput
  orderBy?: Prisma.InvoiceOrderByWithRelationInput | Prisma.InvoiceOrderByWithRelationInput[]
  cursor?: Prisma.InvoiceWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.InvoiceScalarFieldEnum | Prisma.InvoiceScalarFieldEnum[]
}

/**
 * User without action
 */
export type UserDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
}
