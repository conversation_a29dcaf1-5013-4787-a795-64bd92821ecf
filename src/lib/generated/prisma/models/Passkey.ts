
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Passkey` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/client"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Passkey
 * 
 */
export type PasskeyModel = runtime.Types.Result.DefaultSelection<Prisma.$PasskeyPayload>

export type AggregatePasskey = {
  _count: PasskeyCountAggregateOutputType | null
  _avg: PasskeyAvgAggregateOutputType | null
  _sum: PasskeySumAggregateOutputType | null
  _min: PasskeyMinAggregateOutputType | null
  _max: PasskeyMaxAggregateOutputType | null
}

export type PasskeyAvgAggregateOutputType = {
  counter: number | null
}

export type PasskeySumAggregateOutputType = {
  counter: number | null
}

export type PasskeyMinAggregateOutputType = {
  id: string | null
  name: string | null
  publicKey: string | null
  userId: string | null
  credentialID: string | null
  counter: number | null
  deviceType: string | null
  backedUp: boolean | null
  transports: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PasskeyMaxAggregateOutputType = {
  id: string | null
  name: string | null
  publicKey: string | null
  userId: string | null
  credentialID: string | null
  counter: number | null
  deviceType: string | null
  backedUp: boolean | null
  transports: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type PasskeyCountAggregateOutputType = {
  id: number
  name: number
  publicKey: number
  userId: number
  credentialID: number
  counter: number
  deviceType: number
  backedUp: number
  transports: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type PasskeyAvgAggregateInputType = {
  counter?: true
}

export type PasskeySumAggregateInputType = {
  counter?: true
}

export type PasskeyMinAggregateInputType = {
  id?: true
  name?: true
  publicKey?: true
  userId?: true
  credentialID?: true
  counter?: true
  deviceType?: true
  backedUp?: true
  transports?: true
  createdAt?: true
  updatedAt?: true
}

export type PasskeyMaxAggregateInputType = {
  id?: true
  name?: true
  publicKey?: true
  userId?: true
  credentialID?: true
  counter?: true
  deviceType?: true
  backedUp?: true
  transports?: true
  createdAt?: true
  updatedAt?: true
}

export type PasskeyCountAggregateInputType = {
  id?: true
  name?: true
  publicKey?: true
  userId?: true
  credentialID?: true
  counter?: true
  deviceType?: true
  backedUp?: true
  transports?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type PasskeyAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Passkey to aggregate.
   */
  where?: Prisma.PasskeyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Passkeys to fetch.
   */
  orderBy?: Prisma.PasskeyOrderByWithRelationInput | Prisma.PasskeyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.PasskeyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Passkeys from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Passkeys.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Passkeys
  **/
  _count?: true | PasskeyCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: PasskeyAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: PasskeySumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: PasskeyMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: PasskeyMaxAggregateInputType
}

export type GetPasskeyAggregateType<T extends PasskeyAggregateArgs> = {
      [P in keyof T & keyof AggregatePasskey]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregatePasskey[P]>
    : Prisma.GetScalarType<T[P], AggregatePasskey[P]>
}




export type PasskeyGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.PasskeyWhereInput
  orderBy?: Prisma.PasskeyOrderByWithAggregationInput | Prisma.PasskeyOrderByWithAggregationInput[]
  by: Prisma.PasskeyScalarFieldEnum[] | Prisma.PasskeyScalarFieldEnum
  having?: Prisma.PasskeyScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: PasskeyCountAggregateInputType | true
  _avg?: PasskeyAvgAggregateInputType
  _sum?: PasskeySumAggregateInputType
  _min?: PasskeyMinAggregateInputType
  _max?: PasskeyMaxAggregateInputType
}

export type PasskeyGroupByOutputType = {
  id: string
  name: string | null
  publicKey: string
  userId: string
  credentialID: string
  counter: number
  deviceType: string
  backedUp: boolean
  transports: string | null
  createdAt: Date
  updatedAt: Date
  _count: PasskeyCountAggregateOutputType | null
  _avg: PasskeyAvgAggregateOutputType | null
  _sum: PasskeySumAggregateOutputType | null
  _min: PasskeyMinAggregateOutputType | null
  _max: PasskeyMaxAggregateOutputType | null
}

type GetPasskeyGroupByPayload<T extends PasskeyGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<PasskeyGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof PasskeyGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], PasskeyGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], PasskeyGroupByOutputType[P]>
      }
    >
  > 



export type PasskeyWhereInput = {
  AND?: Prisma.PasskeyWhereInput | Prisma.PasskeyWhereInput[]
  OR?: Prisma.PasskeyWhereInput[]
  NOT?: Prisma.PasskeyWhereInput | Prisma.PasskeyWhereInput[]
  id?: Prisma.StringFilter<"Passkey"> | string
  name?: Prisma.StringNullableFilter<"Passkey"> | string | null
  publicKey?: Prisma.StringFilter<"Passkey"> | string
  userId?: Prisma.StringFilter<"Passkey"> | string
  credentialID?: Prisma.StringFilter<"Passkey"> | string
  counter?: Prisma.IntFilter<"Passkey"> | number
  deviceType?: Prisma.StringFilter<"Passkey"> | string
  backedUp?: Prisma.BoolFilter<"Passkey"> | boolean
  transports?: Prisma.StringNullableFilter<"Passkey"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Passkey"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Passkey"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type PasskeyOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrderInput | Prisma.SortOrder
  publicKey?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  credentialID?: Prisma.SortOrder
  counter?: Prisma.SortOrder
  deviceType?: Prisma.SortOrder
  backedUp?: Prisma.SortOrder
  transports?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
}

export type PasskeyWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.PasskeyWhereInput | Prisma.PasskeyWhereInput[]
  OR?: Prisma.PasskeyWhereInput[]
  NOT?: Prisma.PasskeyWhereInput | Prisma.PasskeyWhereInput[]
  name?: Prisma.StringNullableFilter<"Passkey"> | string | null
  publicKey?: Prisma.StringFilter<"Passkey"> | string
  userId?: Prisma.StringFilter<"Passkey"> | string
  credentialID?: Prisma.StringFilter<"Passkey"> | string
  counter?: Prisma.IntFilter<"Passkey"> | number
  deviceType?: Prisma.StringFilter<"Passkey"> | string
  backedUp?: Prisma.BoolFilter<"Passkey"> | boolean
  transports?: Prisma.StringNullableFilter<"Passkey"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Passkey"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Passkey"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}, "id">

export type PasskeyOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrderInput | Prisma.SortOrder
  publicKey?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  credentialID?: Prisma.SortOrder
  counter?: Prisma.SortOrder
  deviceType?: Prisma.SortOrder
  backedUp?: Prisma.SortOrder
  transports?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.PasskeyCountOrderByAggregateInput
  _avg?: Prisma.PasskeyAvgOrderByAggregateInput
  _max?: Prisma.PasskeyMaxOrderByAggregateInput
  _min?: Prisma.PasskeyMinOrderByAggregateInput
  _sum?: Prisma.PasskeySumOrderByAggregateInput
}

export type PasskeyScalarWhereWithAggregatesInput = {
  AND?: Prisma.PasskeyScalarWhereWithAggregatesInput | Prisma.PasskeyScalarWhereWithAggregatesInput[]
  OR?: Prisma.PasskeyScalarWhereWithAggregatesInput[]
  NOT?: Prisma.PasskeyScalarWhereWithAggregatesInput | Prisma.PasskeyScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Passkey"> | string
  name?: Prisma.StringNullableWithAggregatesFilter<"Passkey"> | string | null
  publicKey?: Prisma.StringWithAggregatesFilter<"Passkey"> | string
  userId?: Prisma.StringWithAggregatesFilter<"Passkey"> | string
  credentialID?: Prisma.StringWithAggregatesFilter<"Passkey"> | string
  counter?: Prisma.IntWithAggregatesFilter<"Passkey"> | number
  deviceType?: Prisma.StringWithAggregatesFilter<"Passkey"> | string
  backedUp?: Prisma.BoolWithAggregatesFilter<"Passkey"> | boolean
  transports?: Prisma.StringNullableWithAggregatesFilter<"Passkey"> | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Passkey"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Passkey"> | Date | string
}

export type PasskeyCreateInput = {
  id?: string
  name?: string | null
  publicKey: string
  credentialID: string
  counter: number
  deviceType: string
  backedUp: boolean
  transports?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutPasskeysInput
}

export type PasskeyUncheckedCreateInput = {
  id?: string
  name?: string | null
  publicKey: string
  userId: string
  credentialID: string
  counter: number
  deviceType: string
  backedUp: boolean
  transports?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PasskeyUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  publicKey?: Prisma.StringFieldUpdateOperationsInput | string
  credentialID?: Prisma.StringFieldUpdateOperationsInput | string
  counter?: Prisma.IntFieldUpdateOperationsInput | number
  deviceType?: Prisma.StringFieldUpdateOperationsInput | string
  backedUp?: Prisma.BoolFieldUpdateOperationsInput | boolean
  transports?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutPasskeysNestedInput
}

export type PasskeyUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  publicKey?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  credentialID?: Prisma.StringFieldUpdateOperationsInput | string
  counter?: Prisma.IntFieldUpdateOperationsInput | number
  deviceType?: Prisma.StringFieldUpdateOperationsInput | string
  backedUp?: Prisma.BoolFieldUpdateOperationsInput | boolean
  transports?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PasskeyCreateManyInput = {
  id?: string
  name?: string | null
  publicKey: string
  userId: string
  credentialID: string
  counter: number
  deviceType: string
  backedUp: boolean
  transports?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PasskeyUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  publicKey?: Prisma.StringFieldUpdateOperationsInput | string
  credentialID?: Prisma.StringFieldUpdateOperationsInput | string
  counter?: Prisma.IntFieldUpdateOperationsInput | number
  deviceType?: Prisma.StringFieldUpdateOperationsInput | string
  backedUp?: Prisma.BoolFieldUpdateOperationsInput | boolean
  transports?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PasskeyUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  publicKey?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  credentialID?: Prisma.StringFieldUpdateOperationsInput | string
  counter?: Prisma.IntFieldUpdateOperationsInput | number
  deviceType?: Prisma.StringFieldUpdateOperationsInput | string
  backedUp?: Prisma.BoolFieldUpdateOperationsInput | boolean
  transports?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PasskeyListRelationFilter = {
  every?: Prisma.PasskeyWhereInput
  some?: Prisma.PasskeyWhereInput
  none?: Prisma.PasskeyWhereInput
}

export type PasskeyOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type PasskeyCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  publicKey?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  credentialID?: Prisma.SortOrder
  counter?: Prisma.SortOrder
  deviceType?: Prisma.SortOrder
  backedUp?: Prisma.SortOrder
  transports?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PasskeyAvgOrderByAggregateInput = {
  counter?: Prisma.SortOrder
}

export type PasskeyMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  publicKey?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  credentialID?: Prisma.SortOrder
  counter?: Prisma.SortOrder
  deviceType?: Prisma.SortOrder
  backedUp?: Prisma.SortOrder
  transports?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PasskeyMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  publicKey?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  credentialID?: Prisma.SortOrder
  counter?: Prisma.SortOrder
  deviceType?: Prisma.SortOrder
  backedUp?: Prisma.SortOrder
  transports?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type PasskeySumOrderByAggregateInput = {
  counter?: Prisma.SortOrder
}

export type PasskeyCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.PasskeyCreateWithoutUserInput, Prisma.PasskeyUncheckedCreateWithoutUserInput> | Prisma.PasskeyCreateWithoutUserInput[] | Prisma.PasskeyUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.PasskeyCreateOrConnectWithoutUserInput | Prisma.PasskeyCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.PasskeyCreateManyUserInputEnvelope
  connect?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
}

export type PasskeyUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.PasskeyCreateWithoutUserInput, Prisma.PasskeyUncheckedCreateWithoutUserInput> | Prisma.PasskeyCreateWithoutUserInput[] | Prisma.PasskeyUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.PasskeyCreateOrConnectWithoutUserInput | Prisma.PasskeyCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.PasskeyCreateManyUserInputEnvelope
  connect?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
}

export type PasskeyUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.PasskeyCreateWithoutUserInput, Prisma.PasskeyUncheckedCreateWithoutUserInput> | Prisma.PasskeyCreateWithoutUserInput[] | Prisma.PasskeyUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.PasskeyCreateOrConnectWithoutUserInput | Prisma.PasskeyCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.PasskeyUpsertWithWhereUniqueWithoutUserInput | Prisma.PasskeyUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.PasskeyCreateManyUserInputEnvelope
  set?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
  disconnect?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
  delete?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
  connect?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
  update?: Prisma.PasskeyUpdateWithWhereUniqueWithoutUserInput | Prisma.PasskeyUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.PasskeyUpdateManyWithWhereWithoutUserInput | Prisma.PasskeyUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.PasskeyScalarWhereInput | Prisma.PasskeyScalarWhereInput[]
}

export type PasskeyUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.PasskeyCreateWithoutUserInput, Prisma.PasskeyUncheckedCreateWithoutUserInput> | Prisma.PasskeyCreateWithoutUserInput[] | Prisma.PasskeyUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.PasskeyCreateOrConnectWithoutUserInput | Prisma.PasskeyCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.PasskeyUpsertWithWhereUniqueWithoutUserInput | Prisma.PasskeyUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.PasskeyCreateManyUserInputEnvelope
  set?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
  disconnect?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
  delete?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
  connect?: Prisma.PasskeyWhereUniqueInput | Prisma.PasskeyWhereUniqueInput[]
  update?: Prisma.PasskeyUpdateWithWhereUniqueWithoutUserInput | Prisma.PasskeyUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.PasskeyUpdateManyWithWhereWithoutUserInput | Prisma.PasskeyUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.PasskeyScalarWhereInput | Prisma.PasskeyScalarWhereInput[]
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type PasskeyCreateWithoutUserInput = {
  id?: string
  name?: string | null
  publicKey: string
  credentialID: string
  counter: number
  deviceType: string
  backedUp: boolean
  transports?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PasskeyUncheckedCreateWithoutUserInput = {
  id?: string
  name?: string | null
  publicKey: string
  credentialID: string
  counter: number
  deviceType: string
  backedUp: boolean
  transports?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PasskeyCreateOrConnectWithoutUserInput = {
  where: Prisma.PasskeyWhereUniqueInput
  create: Prisma.XOR<Prisma.PasskeyCreateWithoutUserInput, Prisma.PasskeyUncheckedCreateWithoutUserInput>
}

export type PasskeyCreateManyUserInputEnvelope = {
  data: Prisma.PasskeyCreateManyUserInput | Prisma.PasskeyCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type PasskeyUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.PasskeyWhereUniqueInput
  update: Prisma.XOR<Prisma.PasskeyUpdateWithoutUserInput, Prisma.PasskeyUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.PasskeyCreateWithoutUserInput, Prisma.PasskeyUncheckedCreateWithoutUserInput>
}

export type PasskeyUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.PasskeyWhereUniqueInput
  data: Prisma.XOR<Prisma.PasskeyUpdateWithoutUserInput, Prisma.PasskeyUncheckedUpdateWithoutUserInput>
}

export type PasskeyUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.PasskeyScalarWhereInput
  data: Prisma.XOR<Prisma.PasskeyUpdateManyMutationInput, Prisma.PasskeyUncheckedUpdateManyWithoutUserInput>
}

export type PasskeyScalarWhereInput = {
  AND?: Prisma.PasskeyScalarWhereInput | Prisma.PasskeyScalarWhereInput[]
  OR?: Prisma.PasskeyScalarWhereInput[]
  NOT?: Prisma.PasskeyScalarWhereInput | Prisma.PasskeyScalarWhereInput[]
  id?: Prisma.StringFilter<"Passkey"> | string
  name?: Prisma.StringNullableFilter<"Passkey"> | string | null
  publicKey?: Prisma.StringFilter<"Passkey"> | string
  userId?: Prisma.StringFilter<"Passkey"> | string
  credentialID?: Prisma.StringFilter<"Passkey"> | string
  counter?: Prisma.IntFilter<"Passkey"> | number
  deviceType?: Prisma.StringFilter<"Passkey"> | string
  backedUp?: Prisma.BoolFilter<"Passkey"> | boolean
  transports?: Prisma.StringNullableFilter<"Passkey"> | string | null
  createdAt?: Prisma.DateTimeFilter<"Passkey"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Passkey"> | Date | string
}

export type PasskeyCreateManyUserInput = {
  id?: string
  name?: string | null
  publicKey: string
  credentialID: string
  counter: number
  deviceType: string
  backedUp: boolean
  transports?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type PasskeyUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  publicKey?: Prisma.StringFieldUpdateOperationsInput | string
  credentialID?: Prisma.StringFieldUpdateOperationsInput | string
  counter?: Prisma.IntFieldUpdateOperationsInput | number
  deviceType?: Prisma.StringFieldUpdateOperationsInput | string
  backedUp?: Prisma.BoolFieldUpdateOperationsInput | boolean
  transports?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PasskeyUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  publicKey?: Prisma.StringFieldUpdateOperationsInput | string
  credentialID?: Prisma.StringFieldUpdateOperationsInput | string
  counter?: Prisma.IntFieldUpdateOperationsInput | number
  deviceType?: Prisma.StringFieldUpdateOperationsInput | string
  backedUp?: Prisma.BoolFieldUpdateOperationsInput | boolean
  transports?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type PasskeyUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  publicKey?: Prisma.StringFieldUpdateOperationsInput | string
  credentialID?: Prisma.StringFieldUpdateOperationsInput | string
  counter?: Prisma.IntFieldUpdateOperationsInput | number
  deviceType?: Prisma.StringFieldUpdateOperationsInput | string
  backedUp?: Prisma.BoolFieldUpdateOperationsInput | boolean
  transports?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type PasskeySelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  publicKey?: boolean
  userId?: boolean
  credentialID?: boolean
  counter?: boolean
  deviceType?: boolean
  backedUp?: boolean
  transports?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["passkey"]>

export type PasskeySelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  publicKey?: boolean
  userId?: boolean
  credentialID?: boolean
  counter?: boolean
  deviceType?: boolean
  backedUp?: boolean
  transports?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["passkey"]>

export type PasskeySelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  publicKey?: boolean
  userId?: boolean
  credentialID?: boolean
  counter?: boolean
  deviceType?: boolean
  backedUp?: boolean
  transports?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["passkey"]>

export type PasskeySelectScalar = {
  id?: boolean
  name?: boolean
  publicKey?: boolean
  userId?: boolean
  credentialID?: boolean
  counter?: boolean
  deviceType?: boolean
  backedUp?: boolean
  transports?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type PasskeyOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "publicKey" | "userId" | "credentialID" | "counter" | "deviceType" | "backedUp" | "transports" | "createdAt" | "updatedAt", ExtArgs["result"]["passkey"]>
export type PasskeyInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type PasskeyIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type PasskeyIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $PasskeyPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Passkey"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string | null
    publicKey: string
    userId: string
    credentialID: string
    counter: number
    deviceType: string
    backedUp: boolean
    transports: string | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["passkey"]>
  composites: {}
}

export type PasskeyGetPayload<S extends boolean | null | undefined | PasskeyDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$PasskeyPayload, S>

export type PasskeyCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<PasskeyFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: PasskeyCountAggregateInputType | true
  }

export interface PasskeyDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Passkey'], meta: { name: 'Passkey' } }
  /**
   * Find zero or one Passkey that matches the filter.
   * @param {PasskeyFindUniqueArgs} args - Arguments to find a Passkey
   * @example
   * // Get one Passkey
   * const passkey = await prisma.passkey.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends PasskeyFindUniqueArgs>(args: Prisma.SelectSubset<T, PasskeyFindUniqueArgs<ExtArgs>>): Prisma.Prisma__PasskeyClient<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Passkey that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {PasskeyFindUniqueOrThrowArgs} args - Arguments to find a Passkey
   * @example
   * // Get one Passkey
   * const passkey = await prisma.passkey.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends PasskeyFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, PasskeyFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__PasskeyClient<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Passkey that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PasskeyFindFirstArgs} args - Arguments to find a Passkey
   * @example
   * // Get one Passkey
   * const passkey = await prisma.passkey.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends PasskeyFindFirstArgs>(args?: Prisma.SelectSubset<T, PasskeyFindFirstArgs<ExtArgs>>): Prisma.Prisma__PasskeyClient<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Passkey that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PasskeyFindFirstOrThrowArgs} args - Arguments to find a Passkey
   * @example
   * // Get one Passkey
   * const passkey = await prisma.passkey.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends PasskeyFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, PasskeyFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__PasskeyClient<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Passkeys that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PasskeyFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Passkeys
   * const passkeys = await prisma.passkey.findMany()
   * 
   * // Get first 10 Passkeys
   * const passkeys = await prisma.passkey.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const passkeyWithIdOnly = await prisma.passkey.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends PasskeyFindManyArgs>(args?: Prisma.SelectSubset<T, PasskeyFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Passkey.
   * @param {PasskeyCreateArgs} args - Arguments to create a Passkey.
   * @example
   * // Create one Passkey
   * const Passkey = await prisma.passkey.create({
   *   data: {
   *     // ... data to create a Passkey
   *   }
   * })
   * 
   */
  create<T extends PasskeyCreateArgs>(args: Prisma.SelectSubset<T, PasskeyCreateArgs<ExtArgs>>): Prisma.Prisma__PasskeyClient<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Passkeys.
   * @param {PasskeyCreateManyArgs} args - Arguments to create many Passkeys.
   * @example
   * // Create many Passkeys
   * const passkey = await prisma.passkey.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends PasskeyCreateManyArgs>(args?: Prisma.SelectSubset<T, PasskeyCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Passkeys and returns the data saved in the database.
   * @param {PasskeyCreateManyAndReturnArgs} args - Arguments to create many Passkeys.
   * @example
   * // Create many Passkeys
   * const passkey = await prisma.passkey.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Passkeys and only return the `id`
   * const passkeyWithIdOnly = await prisma.passkey.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends PasskeyCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, PasskeyCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Passkey.
   * @param {PasskeyDeleteArgs} args - Arguments to delete one Passkey.
   * @example
   * // Delete one Passkey
   * const Passkey = await prisma.passkey.delete({
   *   where: {
   *     // ... filter to delete one Passkey
   *   }
   * })
   * 
   */
  delete<T extends PasskeyDeleteArgs>(args: Prisma.SelectSubset<T, PasskeyDeleteArgs<ExtArgs>>): Prisma.Prisma__PasskeyClient<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Passkey.
   * @param {PasskeyUpdateArgs} args - Arguments to update one Passkey.
   * @example
   * // Update one Passkey
   * const passkey = await prisma.passkey.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends PasskeyUpdateArgs>(args: Prisma.SelectSubset<T, PasskeyUpdateArgs<ExtArgs>>): Prisma.Prisma__PasskeyClient<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Passkeys.
   * @param {PasskeyDeleteManyArgs} args - Arguments to filter Passkeys to delete.
   * @example
   * // Delete a few Passkeys
   * const { count } = await prisma.passkey.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends PasskeyDeleteManyArgs>(args?: Prisma.SelectSubset<T, PasskeyDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Passkeys.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PasskeyUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Passkeys
   * const passkey = await prisma.passkey.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends PasskeyUpdateManyArgs>(args: Prisma.SelectSubset<T, PasskeyUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Passkeys and returns the data updated in the database.
   * @param {PasskeyUpdateManyAndReturnArgs} args - Arguments to update many Passkeys.
   * @example
   * // Update many Passkeys
   * const passkey = await prisma.passkey.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Passkeys and only return the `id`
   * const passkeyWithIdOnly = await prisma.passkey.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends PasskeyUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, PasskeyUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Passkey.
   * @param {PasskeyUpsertArgs} args - Arguments to update or create a Passkey.
   * @example
   * // Update or create a Passkey
   * const passkey = await prisma.passkey.upsert({
   *   create: {
   *     // ... data to create a Passkey
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Passkey we want to update
   *   }
   * })
   */
  upsert<T extends PasskeyUpsertArgs>(args: Prisma.SelectSubset<T, PasskeyUpsertArgs<ExtArgs>>): Prisma.Prisma__PasskeyClient<runtime.Types.Result.GetResult<Prisma.$PasskeyPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Passkeys.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PasskeyCountArgs} args - Arguments to filter Passkeys to count.
   * @example
   * // Count the number of Passkeys
   * const count = await prisma.passkey.count({
   *   where: {
   *     // ... the filter for the Passkeys we want to count
   *   }
   * })
  **/
  count<T extends PasskeyCountArgs>(
    args?: Prisma.Subset<T, PasskeyCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], PasskeyCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Passkey.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PasskeyAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends PasskeyAggregateArgs>(args: Prisma.Subset<T, PasskeyAggregateArgs>): Prisma.PrismaPromise<GetPasskeyAggregateType<T>>

  /**
   * Group by Passkey.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {PasskeyGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends PasskeyGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: PasskeyGroupByArgs['orderBy'] }
      : { orderBy?: PasskeyGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, PasskeyGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPasskeyGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Passkey model
 */
readonly fields: PasskeyFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Passkey.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__PasskeyClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Passkey model
 */
export interface PasskeyFieldRefs {
  readonly id: Prisma.FieldRef<"Passkey", 'String'>
  readonly name: Prisma.FieldRef<"Passkey", 'String'>
  readonly publicKey: Prisma.FieldRef<"Passkey", 'String'>
  readonly userId: Prisma.FieldRef<"Passkey", 'String'>
  readonly credentialID: Prisma.FieldRef<"Passkey", 'String'>
  readonly counter: Prisma.FieldRef<"Passkey", 'Int'>
  readonly deviceType: Prisma.FieldRef<"Passkey", 'String'>
  readonly backedUp: Prisma.FieldRef<"Passkey", 'Boolean'>
  readonly transports: Prisma.FieldRef<"Passkey", 'String'>
  readonly createdAt: Prisma.FieldRef<"Passkey", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Passkey", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Passkey findUnique
 */
export type PasskeyFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * Filter, which Passkey to fetch.
   */
  where: Prisma.PasskeyWhereUniqueInput
}

/**
 * Passkey findUniqueOrThrow
 */
export type PasskeyFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * Filter, which Passkey to fetch.
   */
  where: Prisma.PasskeyWhereUniqueInput
}

/**
 * Passkey findFirst
 */
export type PasskeyFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * Filter, which Passkey to fetch.
   */
  where?: Prisma.PasskeyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Passkeys to fetch.
   */
  orderBy?: Prisma.PasskeyOrderByWithRelationInput | Prisma.PasskeyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Passkeys.
   */
  cursor?: Prisma.PasskeyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Passkeys from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Passkeys.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Passkeys.
   */
  distinct?: Prisma.PasskeyScalarFieldEnum | Prisma.PasskeyScalarFieldEnum[]
}

/**
 * Passkey findFirstOrThrow
 */
export type PasskeyFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * Filter, which Passkey to fetch.
   */
  where?: Prisma.PasskeyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Passkeys to fetch.
   */
  orderBy?: Prisma.PasskeyOrderByWithRelationInput | Prisma.PasskeyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Passkeys.
   */
  cursor?: Prisma.PasskeyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Passkeys from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Passkeys.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Passkeys.
   */
  distinct?: Prisma.PasskeyScalarFieldEnum | Prisma.PasskeyScalarFieldEnum[]
}

/**
 * Passkey findMany
 */
export type PasskeyFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * Filter, which Passkeys to fetch.
   */
  where?: Prisma.PasskeyWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Passkeys to fetch.
   */
  orderBy?: Prisma.PasskeyOrderByWithRelationInput | Prisma.PasskeyOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Passkeys.
   */
  cursor?: Prisma.PasskeyWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Passkeys from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Passkeys.
   */
  skip?: number
  distinct?: Prisma.PasskeyScalarFieldEnum | Prisma.PasskeyScalarFieldEnum[]
}

/**
 * Passkey create
 */
export type PasskeyCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * The data needed to create a Passkey.
   */
  data: Prisma.XOR<Prisma.PasskeyCreateInput, Prisma.PasskeyUncheckedCreateInput>
}

/**
 * Passkey createMany
 */
export type PasskeyCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Passkeys.
   */
  data: Prisma.PasskeyCreateManyInput | Prisma.PasskeyCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Passkey createManyAndReturn
 */
export type PasskeyCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * The data used to create many Passkeys.
   */
  data: Prisma.PasskeyCreateManyInput | Prisma.PasskeyCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Passkey update
 */
export type PasskeyUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * The data needed to update a Passkey.
   */
  data: Prisma.XOR<Prisma.PasskeyUpdateInput, Prisma.PasskeyUncheckedUpdateInput>
  /**
   * Choose, which Passkey to update.
   */
  where: Prisma.PasskeyWhereUniqueInput
}

/**
 * Passkey updateMany
 */
export type PasskeyUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Passkeys.
   */
  data: Prisma.XOR<Prisma.PasskeyUpdateManyMutationInput, Prisma.PasskeyUncheckedUpdateManyInput>
  /**
   * Filter which Passkeys to update
   */
  where?: Prisma.PasskeyWhereInput
  /**
   * Limit how many Passkeys to update.
   */
  limit?: number
}

/**
 * Passkey updateManyAndReturn
 */
export type PasskeyUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * The data used to update Passkeys.
   */
  data: Prisma.XOR<Prisma.PasskeyUpdateManyMutationInput, Prisma.PasskeyUncheckedUpdateManyInput>
  /**
   * Filter which Passkeys to update
   */
  where?: Prisma.PasskeyWhereInput
  /**
   * Limit how many Passkeys to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Passkey upsert
 */
export type PasskeyUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * The filter to search for the Passkey to update in case it exists.
   */
  where: Prisma.PasskeyWhereUniqueInput
  /**
   * In case the Passkey found by the `where` argument doesn't exist, create a new Passkey with this data.
   */
  create: Prisma.XOR<Prisma.PasskeyCreateInput, Prisma.PasskeyUncheckedCreateInput>
  /**
   * In case the Passkey was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.PasskeyUpdateInput, Prisma.PasskeyUncheckedUpdateInput>
}

/**
 * Passkey delete
 */
export type PasskeyDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
  /**
   * Filter which Passkey to delete.
   */
  where: Prisma.PasskeyWhereUniqueInput
}

/**
 * Passkey deleteMany
 */
export type PasskeyDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Passkeys to delete
   */
  where?: Prisma.PasskeyWhereInput
  /**
   * Limit how many Passkeys to delete.
   */
  limit?: number
}

/**
 * Passkey without action
 */
export type PasskeyDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Passkey
   */
  select?: Prisma.PasskeySelect<ExtArgs> | null
  /**
   * Omit specific fields from the Passkey
   */
  omit?: Prisma.PasskeyOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.PasskeyInclude<ExtArgs> | null
}
