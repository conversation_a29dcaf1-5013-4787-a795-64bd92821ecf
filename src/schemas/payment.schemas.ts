import { z } from "@/lib/zod";
import { invoiceItemSchema } from "@/schemas/invoice.schemas";

export const billingPortalSessionSchema = z.object({
  stripeCustomerId: z.string(),
  returnUrl: z.string().url(),
});

export const checkoutSessionSchema = z.object({
  priceId: z.string().optional(),
  stripeCustomerId: z.string().optional(),
  returnUrl: z.string().url(),
});

export const stripeAccountSchema = z.object({
  userId: z.string(),
  email: z.string().email(),
});

export const stripeAccountLinkSchema = z.object({
  stripeAccountId: z.string(),
});

export const stripeConnectSessionLinkSchema = z
  .object({
    stripeAccountId: z.string().optional(),
  })
  .merge(stripeAccountSchema);

export const removeConnectedAccountSchema = z.object({
  stripeAccountId: z.string(),
});

export const invoiceCheckoutSessionSchema = z.object({
  invoiceId: z.string(),
  items: z.array(invoiceItemSchema),
  stripeAccountId: z.string(),
});
