import { z } from "@/lib/zod";

export const invoiceItemSchema = z.object({
  id: z.string(),
  date: z.string(),
  description: z.string(),
  rate: z.number(),
  subtotal: z.number(),
  tax: z.number(),
  units: z.number(),
});

export const invoiceCreateSchema = z.object({
  contactId: z.string().describe("The Id of the contact"),
  projectId: z.string().optional().describe("The Id of the project"),
  items: z
    .array(invoiceItemSchema)
    .optional()
    .describe("The items of the invoice"),
  fromAddress: z.string().optional().describe("The from address"),
  fromCompany: z.string().optional().describe("The from company"),
  fromName: z.string().optional().describe("The from name"),
  fromEmail: z.string().optional().describe("The from email"),
  toAddress: z.string().optional().describe("The to address"),
  toCompany: z.string().optional().describe("The to company"),
  toEmail: z.string().optional().describe("The to email"),
  toName: z.string().optional().describe("The to name"),
  logoImage: z.string().optional().describe("The logo image"),
  isStripeEnabled: z.boolean().optional().describe("Whether Stripe is enabled"),
  isPaypalEnabled: z.boolean().optional().describe("Whether Paypal is enabled"),
  isCashappEnabled: z
    .boolean()
    .optional()
    .describe("Whether Cashapp is enabled"),
  isZelleEnabled: z.boolean().optional().describe("Whether Zelle is enabled"),
});

export const invoiceUpdateSchema = z.object({
  contactId: z.string().optional().describe("The Id of the contact"),
  projectId: z.string().optional().describe("The Id of the project"),
  amountDue: z.number().optional().describe("The amount due"),
  amountPaid: z.number().optional().describe("The amount paid"),
  discount: z.number().optional().describe("The discount"),
  dueOn: z.string().optional().describe("The due date"),
  fromAddress: z.string().optional().describe("The from address"),
  fromCompany: z.string().optional().describe("The from company"),
  fromName: z.string().optional().describe("The from name"),
  fromEmail: z.string().optional().describe("The from email"),
  invoiceNumber: z.string().optional().describe("The invoice number"),
  issuedOn: z.string().optional().describe("The issued on date"),
  notes: z.string().optional().describe("The notes of the invoice"),
  paymentDate: z.string().optional().describe("The payment date"),
  logoImage: z.string().optional().describe("The logo image"),
  fee: z.number().optional().describe("The fee"),
  net: z.number().optional().describe("The net"),
  paymentMethod: z
    .enum(["stripe", "cashapp", "zelle"])
    .optional()
    .describe("The payment method used to pay the invoice"),
  transactionId: z
    .string()
    .optional()
    .describe("The transaction id of the payment"),
  toAddress: z.string().optional().describe("The to address"),
  toCompany: z.string().optional().describe("The to company"),
  toEmail: z.string().optional().describe("The to email"),
  toName: z.string().optional().describe("The to name"),
  total: z.number().optional().describe("The total"),
  isStripeEnabled: z.boolean().optional().describe("Whether Stripe is enabled"),
  isPaypalEnabled: z.boolean().optional().describe("Whether Paypal is enabled"),
  isCashappEnabled: z
    .boolean()
    .optional()
    .describe("Whether Cashapp is enabled"),
  isZelleEnabled: z.boolean().optional().describe("Whether Zelle is enabled"),
  paypallAccountId: z.string().optional().describe("The Paypal account id"),
  cashappAccountId: z.string().optional().describe("The Cashapp account id"),
  zelleAccountId: z.string().optional().describe("The Zelle account id"),
  type: z
    .enum(["one_time", "recurring"])
    .optional()
    .describe("The invoice type"),
  sentDate: z.string().optional().describe("The sent date"),
  status: z
    .enum(["draft", "sent", "paid", "read", "overdue"])
    .optional()
    .describe("The invoice status"),
  taxLabel: z.string().optional().describe("The tax label"),
  subtotal: z.number().optional().describe("The subtotal"),
  tax: z.number().optional().describe("The tax"),
  taxTotal: z.number().optional().describe("The tax total"),
  items: z
    .array(invoiceItemSchema)
    .optional()
    .describe("The items of the invoice"),
  unitsType: z.string().optional().describe("The units type"),
});
