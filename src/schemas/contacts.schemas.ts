import { z } from "@/lib/zod";

export const contactCreateSchema = z.object({
  firstName: z
    .string()
    .min(1, "First name is required.")
    .trim()
    .describe("The first name of the contact."),
  lastName: z
    .string()
    .min(1, "Last name is required.")
    .trim()
    .describe("The last name of the contact."),
  email: z
    .string()
    .email({ message: "Please enter a valid email." })
    .trim()
    .describe("The email address of the contact."),
  phone: z
    .string()
    .trim()
    .optional()
    .describe("The phone number of the contact."),
  type: z
    .enum(["client", "lead", "other"])
    .default("lead")
    .describe("The type of the contact."),
  company: z.string().trim().optional().describe("The company of the contact."),
  website: z.string().trim().optional().describe("The website of the contact."),
  address: z.string().trim().optional().describe("The address of the contact."),
  notes: z.string().trim().optional().describe("Notes about the contact."),
});

export const contactUpdateSchema = z.object({
  firstName: z
    .string()
    .min(1, "First name is required.")
    .trim()
    .optional()
    .describe("The first name of the contact."),
  lastName: z
    .string()
    .min(1, "Last name is required.")
    .trim()
    .optional()
    .describe("The last name of the contact."),
  email: z
    .string()
    .email({ message: "Please enter a valid email." })
    .trim()
    .optional()
    .describe("The email address of the contact."),
  phone: z
    .string()
    .trim()
    .optional()
    .describe("The phone number of the contact."),
  type: z
    .enum(["client", "lead", "other"])
    .optional()
    .describe("The type of the contact."),
  company: z.string().trim().optional().describe("The company of the contact."),
  website: z.string().trim().optional().describe("The website of the contact."),
  address: z.string().trim().optional().describe("The address of the contact."),
  notes: z.string().trim().optional().describe("Notes about the contact."),
  archived: z.boolean().optional().describe("Whether the contact is archived."),
});
