import { z } from "@/lib/zod";

export const projectCreateSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required.")
    .trim()
    .describe("The name of the project."),
  description: z
    .string()
    .trim()
    .optional()
    .describe("The description of the project."),
  status: z.string().default("planned").describe("The status of the project."),
  endDate: z.string().optional().describe("The end date of the project."),
  contactId: z.string().optional().describe("The Id of the contact"),
});

export const projectUpdateSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required.")
    .trim()
    .optional()
    .describe("The name of the project."),
  description: z
    .string()
    .min(1, "Description is required.")
    .trim()
    .optional()
    .describe("The description of the project."),
  status: z.string().optional().describe("The status of the project."),
  notes: z.string().optional().describe("The notes of the project."),
  endDate: z.string().optional().describe("The end date of the project."),
  contactId: z.string().optional().describe("The Id of the contact"),
  archived: z.boolean().optional().describe("Whether the project is archived."),
});
