export const currencies = {
  usd: "USD - United States Dollar",
  aed: "AED - United Arab Emirates Dirham",
  afn: "AFN - Afghan Afghani",
  all: "ALL - Albanian Lek",
  amd: "AMD - Armenian Dram",
  ang: "ANG - Netherlands Antillean Guilder",
  aoa: "AOA - Angolan Kwanza",
  ars: "ARS - Argentine Peso",
  aud: "AUD - Australian Dollar",
  awg: "AWG - Aruban Florin",
  azn: "AZN - Azerbaijani Manat",
  bam: "BAM - Bosnia & Herzegovina Convertible Mark",
  bbd: "BBD - Barbadian Dollar",
  bdt: "BDT - Bangladeshi Taka",
  bgn: "BGN - Bulgarian Lev",
  bif: "BIF - Burundian Franc",
  bmd: "BMD - Bermudian Dollar",
  bnd: "BND - Brunei Dollar",
  bob: "BOB - Bolivian Boliviano",
  brl: "BRL - Brazilian Real",
  bsd: "BSD - Bahamian Dollar",
  bwp: "BWP - Botswana Pula",
  byn: "BYN - Belarusian Ruble",
  bzd: "BZD - Belize Dollar",
  cad: "CAD - Canadian Dollar",
  cdf: "CDF - Congolese Franc",
  chf: "CHF - Swiss Franc",
  clp: "CLP - Chilean Peso",
  cny: "CNY - Chinese Yuan",
  cop: "COP - Colombian Peso",
  crc: "CRC - Costa Rican Colón",
  cve: "CVE - Cape Verdean Escudo",
  czk: "CZK - Czech Koruna",
  djf: "DJF - Djiboutian Franc",
  dkk: "DKK - Danish Krone",
  dop: "DOP - Dominican Peso",
  dzd: "DZD - Algerian Dinar",
  egp: "EGP - Egyptian Pound",
  etb: "ETB - Ethiopian Birr",
  eur: "EUR - Euro",
  fjd: "FJD - Fijian Dollar",
  fkp: "FKP - Falkland Islands Pound",
  gbp: "GBP - British Pound",
  gel: "GEL - Georgian Lari",
  gip: "GIP - Gibraltar Pound",
  gmd: "GMD - Gambian Dalasi",
  gnf: "GNF - Guinean Franc",
  gtq: "GTQ - Guatemalan Quetzal",
  gyd: "GYD - Guyanese Dollar",
  hkd: "HKD - Hong Kong Dollar",
  hnl: "HNL - Honduran Lempira",
  htg: "HTG - Haitian Gourde",
  huf: "HUF - Hungarian Forint",
  idr: "IDR - Indonesian Rupiah",
  ils: "ILS - Israeli Shekel",
  inr: "INR - Indian Rupee",
  isk: "ISK - Icelandic Króna",
  jmd: "JMD - Jamaican Dollar",
  jpy: "JPY - Japanese Yen",
  kes: "KES - Kenyan Shilling",
  kgs: "KGS - Kyrgyzstani Som",
  khr: "KHR - Cambodian Riel",
  kmf: "KMF - Comorian Franc",
  krw: "KRW - South Korean Won",
  kyd: "KYD - Cayman Islands Dollar",
  kzt: "KZT - Kazakhstani Tenge",
  lak: "LAK - Lao Kip",
  lbp: "LBP - Lebanese Pound",
  lkr: "LKR - Sri Lankan Rupee",
  lrd: "LRD - Liberian Dollar",
  lsl: "LSL - Lesotho Loti",
  mad: "MAD - Moroccan Dirham",
  mdl: "MDL - Moldovan Leu",
  mga: "MGA - Malagasy Ariary",
  mkd: "MKD - Macedonian Denar",
  mmk: "MMK - Burmese Kyat",
  mnt: "MNT - Mongolian Tögrög",
  mop: "MOP - Macanese Pataca",
  mur: "MUR - Mauritian Rupee",
  mvr: "MVR - Maldivian Rufiyaa",
  mwk: "MWK - Malawian Kwacha",
  mxn: "MXN - Mexican Peso",
  myr: "MYR - Malaysian Ringgit",
  mzn: "MZN - Mozambican Metical",
  nad: "NAD - Namibian Dollar",
  ngn: "NGN - Nigerian Naira",
  nio: "NIO - Nicaraguan Córdoba",
  nok: "NOK - Norwegian Krone",
  npr: "NPR - Nepalese Rupee",
  nzd: "NZD - New Zealand Dollar",
  pab: "PAB - Panamanian Balboa",
  pen: "PEN - Peruvian Sol",
  pgk: "PGK - Papua New Guinean Kina",
  php: "PHP - Philippine Peso",
  pkr: "PKR - Pakistani Rupee",
  pln: "PLN - Polish Zloty",
  pyg: "PYG - Paraguayan Guarani",
  qar: "QAR - Qatari Riyal",
  ron: "RON - Romanian Leu",
  rsd: "RSD - Serbian Dinar",
  rub: "RUB - Russian Ruble",
  rwf: "RWF - Rwandan Franc",
  sar: "SAR - Saudi Riyal",
  sbd: "SBD - Solomon Islands Dollar",
  scr: "SCR - Seychellois Rupee",
  sek: "SEK - Swedish Krona",
  sgd: "SGD - Singapore Dollar",
  shp: "SHP - Saint Helena Pound",
  sle: "SLE - Sierra Leonean Leone",
  sos: "SOS - Somali Shilling",
  srd: "SRD - Surinamese Dollar",
  std: "STD - São Tomé and Príncipe Dobra",
  szl: "SZL - Swazi Lilangeni",
  thb: "THB - Thai Baht",
  tjs: "TJS - Tajikistani Somoni",
  top: "TOP - Tongan Paʻanga",
  try: "TRY - Turkish Lira",
  ttd: "TTD - Trinidad and Tobago Dollar",
  twd: "TWD - New Taiwan Dollar",
  tzs: "TZS - Tanzanian Shilling",
  uah: "UAH - Ukrainian Hryvnia",
  ugx: "UGX - Ugandan Shilling",
  uyu: "UYU - Uruguayan Peso",
  uzs: "UZS - Uzbekistani Som",
  vnd: "VND - Vietnamese Dong",
  vuv: "VUV - Vanuatu Vatu",
  wst: "WST - Samoan Tala",
  xaf: "XAF - Central African CFA Franc",
  xcd: "XCD - East Caribbean Dollar",
  xof: "XOF - West African CFA Franc",
  xpf: "XPF - CFP Franc",
  yer: "YER - Yemeni Rial",
  zar: "ZAR - South African Rand",
  zmw: "ZMW - Zambian Kwacha",
};

export type Currency = keyof typeof currencies;

export const currenciesKeys = Object.keys(currencies) as Currency[];

export const currenciesValues = Object.values(currencies);

export function getCurrencyName(currency: Currency) {
  return currencies[currency] || currency;
}

export function getCurrencyCode(currency: Currency) {
  return currency.toUpperCase();
}
