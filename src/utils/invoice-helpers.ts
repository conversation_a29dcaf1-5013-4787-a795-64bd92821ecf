import { nanoid } from "@/libs/nanoid";
import { type Invoice, type InvoiceItem } from "@/types/invoice.types";

export function createInvoiceItem(): InvoiceItem {
  return {
    id: nanoid(),
    date: "",
    rate: 0,
    tax: 0,
    units: 0,
    subtotal: 0,
    description: "",
  };
}

export const getInvoiceSubtotal = (items: InvoiceItem[] = []) => {
  const subtotal = items.reduce(function (
    accumulator: number,
    item: InvoiceItem,
  ) {
    return accumulator + item.subtotal;
  }, 0);
  return subtotal;
};

export const getInvoiceTotal = (invoice: Invoice) => {
  return (
    getInvoiceSubtotal(invoice?.items) +
    Number(invoice?.tax) -
    Number(invoice?.discount)
  );
};

export const getItemSubtotal = (item: InvoiceItem) => {
  return Number(item.units) * Number(item.rate);
};

export const defaultInvoice: Partial<Invoice> = {
  amountDue: 0,
  amountPaid: 0,
  subtotal: 0,
  total: 0,
  tax: 0,
  discount: 0,
  sentDate: "",
  invoiceNumber: "",
  status: "draft",
  issuedOn: "",
  dueOn: "",
  type: "one_time",
  fromCompany: "",
  fromAddress: "",
  fromName: "",
  toAddress: "",
  toCompany: "",
  toEmail: "",
  toName: "",
  items: [],
  notes: "",
  paymentDate: "",
  taxLabel: "",
  fromEmail: "",
  fee: 0,
  net: 0,
  id: "",
};
