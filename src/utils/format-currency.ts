/**
 * Formatter for USD currency representation.
 * Uses Intl.NumberFormat with en-US locale.
 */
const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2,
});

/**
 * Formats a numeric amount as a USD currency string.
 *
 * @param amount - The monetary amount to format. Defaults to 0 if null or undefined.
 * @returns The formatted currency string, e.g., "$1,234.56".
 */
export const formatCurrency = (amount: number | null | undefined): string => {
  return currencyFormatter.format(amount ?? 0);
};

/**
 * Formats a Stripe amount (in cents) as a USD currency string.
 *
 * @param amount - The amount in cents to format. Defaults to 0 if null or undefined.
 * @returns The formatted currency string, e.g., "$12.34".
 */
export const formatStripeCurrency = (
  amount: number | null | undefined,
): string => {
  return currencyFormatter.format((amount ?? 0) / 100);
};
