"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDialog } from "@/hooks/use-dialog";
import { useProjectDeleteMutation } from "@/queries/project.queries";
import { type Project } from "@/types/project.types";
import { IconDots, IconTrash } from "@tabler/icons-react";
import { useRouter } from "next/navigation";

interface Props {
  id: string;
  project: Project;
}

export function ProjectPageActionsMenu({ id, project }: Props) {
  const router = useRouter();
  const [openDeleteDialog, openDeleteDialogHandlers] = useDialog();
  // const [openEditDialog, openEditDialogHandlers] = useDialog();

  const deleteMutation = useProjectDeleteMutation();

  async function onDelete() {
    await deleteMutation.mutateAsync({ id });
    router.push("/projects");
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className=" text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
          >
            <IconDots size={18} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[180px]">
          {/* <DropdownMenuItem onClick={openEditDialogHandlers.open}>
            <IconPencil className="mr-2 h-4 w-4" />
            <span>Edit</span>
          </DropdownMenuItem> */}
          <DropdownMenuItem
            className="!text-red-500 hover:!bg-red-500/5"
            onClick={openDeleteDialogHandlers.open}
          >
            <IconTrash className="mr-2 h-4 w-4" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DeleteDialog
        title="Project"
        open={openDeleteDialog}
        onClose={openDeleteDialogHandlers.close}
        onDelete={onDelete}
        loading={deleteMutation.isPending}
      />
    </>
  );
}
