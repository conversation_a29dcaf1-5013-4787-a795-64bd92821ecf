"use client";

import { ProjectActionsMenu } from "@/components/projects/project-actions-menu";
import { ProjectCreateDialog } from "@/components/projects/project-create-dialog";
import { ProjectStatusBadge } from "@/components/projects/project-status-badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable, DataTableFacetedFilter } from "@/components/ui/data-table";
import { EmptyState } from "@/components/ui/empty-state";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Loader } from "@/components/ui/loader";
import {
  PageContent,
  PageTitle,
  PageWrapper,
} from "@/components/ui/page-structure";
import { SearchInput } from "@/components/ui/search-input";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useDialog } from "@/hooks/use-dialog";
import {
  useInfiniteProjects,
  useProjectDeleteManyMutation,
} from "@/queries/project.queries";
import type {
  InfiniteProjectsData,
  Project,
  ProjectsOutput,
  ProjectStatus,
} from "@/types/project.types";
import { formatDate } from "@/utils/format-date";
import { getFullName } from "@/utils/get-full-name";
import { getInitials } from "@/utils/get-initials";
import {
  IconBriefcase,
  IconEye,
  IconMail,
  IconPlus,
} from "@tabler/icons-react";
import { type ColumnDef } from "@tanstack/react-table";
// Removed unused import
import Link from "next/link";
import { isEmpty } from "radash";
import { useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";

export const formatProjects = (projects: InfiniteProjectsData | undefined) => {
  let data = [] as Project[];
  if (projects?.pages) {
    for (const page of projects?.pages) {
      data = [...data, ...page.data];
    }
    return data.map((project) => ({
      ...project,
      endDate: project.endDate ? formatDate(project.endDate) : "-",
    }));
  }
  return [];
};

interface Props {
  initialData?: ProjectsOutput;
}

export function ProjectsView({ initialData }: Props) {
  const { ref, inView } = useInView();
  const [openCreateModal, openCreateModalHandlers] = useDialog();
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [searchString, setSearchString] = useDebouncedState("", 250); // Removed unused contactTypes state

  const projects = useInfiniteProjects({ searchString });

  useEffect(() => {
    if (projects.hasNextPage && inView) {
      projects.fetchNextPage();
    }
  }, [inView, projects]);

  const deleteManyMutation = useProjectDeleteManyMutation();

  async function deleteManyProjects() {
    setSelectedIds([]);
    await deleteManyMutation.mutateAsync({ ids: selectedIds });
  }

  const data = useMemo(() => formatProjects(projects.data), [projects]);

  const noSearchResults = isEmpty(data) && !isEmpty(searchString);

  const columns = useMemo<ColumnDef<Project>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "name",
        header: "Project name",
        cell: (info) => (
          <Link href={`/projects/${info?.row.original.id}`}>
            <div className="flex items-center space-x-3 hover:underline hover:underline-offset-4">
              <span className="font-medium">{info?.row.original.name}</span>
            </div>
          </Link>
        ),
      },
      {
        accessorKey: "contact",
        header: () => "Client",
        cell: (info) => (
          <div>
            {info.row.original.contact && (
              <div className="flex items-center space-x-2">
                <HoverCard>
                  <HoverCardTrigger asChild>
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="cursor-default text-white uppercase">
                        {getInitials(getFullName(info.row.original.contact), 2)}
                      </AvatarFallback>
                    </Avatar>
                  </HoverCardTrigger>
                  <HoverCardContent className="w-[300px]">
                    <div className="flex space-x-4">
                      <Avatar>
                        <AvatarFallback className="cursor-default text-white uppercase">
                          {getInitials(
                            getFullName(info.row.original.contact),
                            2,
                          )}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-1 truncate">
                        <h4 className="font-semibold">
                          {getFullName(info.row.original.contact)}
                        </h4>
                        <p className="truncate text-sm text-gray-600">
                          {info.row.original.contact?.email}
                        </p>
                        <div className="flex items-center space-x-2 pt-2">
                          <Button
                            href={`mailto:${info.row.original.contact?.email}`}
                            leftIcon={<IconMail size={16} />}
                            variant="secondary"
                            size="sm"
                          >
                            Message
                          </Button>
                          <Button
                            href={`/contacts/${info.row.original.contact?.id}`}
                            leftIcon={<IconEye size={16} />}
                            variant="secondary"
                            size="sm"
                          >
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  </HoverCardContent>
                </HoverCard>
                <div>
                  <p className="text-gray-900">
                    {getFullName(info.row.original.contact)}
                  </p>
                </div>
              </div>
            )}

            {!info.row.original.contact && (
              <div>
                <p>-</p>
              </div>
            )}
          </div>
        ),
      },
      {
        accessorKey: "status",
        header: () => "Status",
        cell: (info) => (
          <ProjectStatusBadge
            status={info.row.original.status as ProjectStatus}
          />
        ),
      },
      {
        accessorKey: "endDate",
        header: () => "End date",
      },
      {
        accessorKey: "createdAt",
        header: "Created",
        cell: (info) => <div>{formatDate(info.row.original.createdAt)}</div>,
      },
      {
        id: "actions",
        cell: (info) => (
          <div className="text-right">
            <ProjectActionsMenu id={info?.row.original.id} />
          </div>
        ),
      },
    ],
    [],
  );

  return (
    <PageWrapper>
      <div className="flex items-center justify-between">
        <PageTitle>Projects</PageTitle>
        <div>
          <Button
            leftIcon={<IconPlus size={16} />}
            onClick={openCreateModalHandlers.open}
          >
            New project
          </Button>
        </div>
      </div>

      <div className="mt-4 flex space-x-3">
        <SearchInput
          placeholder="Search projects"
          defaultValue={searchString}
          onChange={(event) => setSearchString(event.currentTarget.value)}
          disabled={selectedIds.length > 0}
        />
        {/* Uncomment and update filter options as needed */}
        {/* <DataTableFacetedFilter
          options={[
            { label: "Volunteer", value: "volunteer" },
            { label: "Sponsor", value: "sponsor" },
            { label: "Partner", value: "partner" },
          ]}
          title="Type"
          setValues={setContactTypes}
          values={Array.from(contactTypes)}
        />
        {!isEmpty(Array.from(contactTypes)) && (
          <Button
            variant="ghost"
            onClick={() => setContactTypes(new Set([]))}
            rightIcon={<IconX size={16} />}
          >
            Reset
          </Button>
        )} */}
      </div>

      <PageContent>
        <DataTable
          data={data}
          columns={columns}
          setSelectedIds={setSelectedIds}
          selectedIds={selectedIds}
          onDelete={deleteManyProjects}
          isLoading={deleteManyMutation.isPending || projects.isLoading}
          deleteModalTitle="Project"
          emptyState={
            <>
              {!noSearchResults && (
                <EmptyState
                  title="No projects yet"
                  subtitle="Get started by creating a new project."
                  icon={<IconBriefcase size={40} />}
                  actionButton={
                    <Button
                      leftIcon={<IconPlus size={16} />}
                      onClick={openCreateModalHandlers.open}
                    >
                      New project
                    </Button>
                  }
                />
              )}

              {noSearchResults && (
                <EmptyState
                  title="No search results"
                  subtitle="Please check the spelling or filter criteria"
                  icon={<IconBriefcase size={40} />}
                />
              )}
            </>
          }
        />
      </PageContent>

      <div ref={ref} className="text-center">
        {projects.isFetchingNextPage && <Loader className="mt-5" />}
      </div>

      <ProjectCreateDialog
        open={openCreateModal}
        onClose={openCreateModalHandlers.close}
      />
    </PageWrapper>
  );
}
