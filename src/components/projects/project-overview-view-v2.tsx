"use client";

import { ProjectEditDialog } from "@/components/projects/project-edit-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { DatePicker } from "@/components/ui/date-picker";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { useDialog } from "@/hooks/use-dialog";
import {
  useProjectById,
  useProjectUpdateMutation,
} from "@/queries/project.queries";
import { formatCurrency } from "@/utils/format-currency";
import { getFullName } from "@/utils/get-full-name";
import {
  IconCalendarDue,
  IconCheck,
  IconClock,
  IconCurrencyDollar,
  IconEye,
  IconFile,
  IconFileText,
  IconMail,
  IconPencil,
  IconPlus,
  IconReceiptDollar,
  IconTarget,
  IconUser,
} from "@tabler/icons-react";
import { useEffect, useState } from "react";
import TextareaAutosize from "react-textarea-autosize";

interface Props {
  projectId: string;
}

export function ProjectOverviewViewV2({ projectId }: Props) {
  const [notes, setNotes] = useState("");
  const [notesEditMode, setNotesEditMode] = useState(false);
  const [projectEndDate, setProjectEndDate] = useState<Date | null>(null);
  const [editDetailsMode, editDetailsModeHandler] = useDialog();

  const project = useProjectById(projectId);
  const updateMutation = useProjectUpdateMutation();

  useEffect(() => {
    if (project?.data?.notes) {
      setNotes(project?.data?.notes);
    }
    if (project?.data?.endDate) {
      setProjectEndDate(new Date(project?.data?.endDate));
    }
  }, [project?.data?.notes, project?.data?.endDate]);

  const handleEndDateUpdate = async (date: Date | undefined) => {
    if (!date) return;
    setProjectEndDate(date);
    return await updateMutation.mutateAsync({
      projectId,
      endDate: date.toISOString(),
    });
  };

  const handleNotesUpdate = async () => {
    await updateMutation.mutateAsync({
      projectId,
      notes,
    });
    setNotesEditMode(false);
  };

  // Project stats calculated from real data
  const projectData = project?.data;

  // TODO: Add invoice query by project ID to get real invoice stats
  // For now using placeholder data - this should be replaced with actual invoice calculations
  const issued = 15420;
  const outstanding = 2920;
  const overdue = 0;
  const paid = 12500;

  // Calculate days remaining from project end date
  const daysRemaining = projectData?.endDate
    ? Math.max(
        0,
        Math.ceil(
          (new Date(projectData.endDate).getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24),
        ),
      )
    : null;

  // Project status-based progress
  const getProjectProgress = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
        return 100;
      case "in progress":
        return 60;
      case "planned":
        return 10;
      case "paused":
        return 30;
      default:
        return 0;
    }
  };

  const projectProgress = getProjectProgress(projectData?.status || "");

  return (
    <div className="space-y-8">
      {/* Invoice Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Issued</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(issued)}
                </p>
              </div>
              <div className="rounded-xl bg-gray-50 p-3">
                <IconCurrencyDollar className="h-6 w-6 text-gray-700" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Outstanding</p>
                <p className="text-2xl font-bold text-amber-600">
                  {formatCurrency(outstanding)}
                </p>
              </div>
              <div className="rounded-xl bg-amber-50 p-3">
                <IconReceiptDollar className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Overdue</p>
                <p className="text-2xl font-bold text-red-600">
                  {formatCurrency(overdue)}
                </p>
              </div>
              <div className="rounded-xl bg-red-50 p-3">
                <IconClock className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Paid</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(paid)}
                </p>
              </div>
              <div className="rounded-xl bg-green-50 p-3">
                <IconCheck className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-8 lg:grid-cols-3">
        {/* Main Content - Left Side */}
        <div className="space-y-8 lg:col-span-2">
          {/* Project Progress */}
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gray-100 p-2">
                    <IconTarget className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Project Progress
                    </h3>
                    <p className="text-sm text-gray-500">
                      Track completion status
                    </p>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-700">
                    Project Status Progress
                  </span>
                  <span className="font-semibold text-gray-900">
                    {projectProgress}%
                  </span>
                </div>
                <Progress value={projectProgress} className="h-3" />
              </div>

              <div className="grid gap-4 sm:grid-cols-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {projectData?.status === "completed" ? "1" : "0"}
                  </div>
                  <div className="text-sm text-gray-500">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {projectData?.status === "in progress" ? "1" : "0"}
                  </div>
                  <div className="text-sm text-gray-500">In Progress</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">
                    {projectData?.status === "planned" ||
                    projectData?.status === "paused"
                      ? "1"
                      : "0"}
                  </div>
                  <div className="text-sm text-gray-500">Pending</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Activity */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gray-100 p-2">
                    <IconCheck className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Project Activity
                    </h3>
                    <p className="text-sm text-gray-500">
                      Recent project updates
                    </p>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  leftIcon={<IconPlus size={16} />}
                >
                  Add Update
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="group">
                  <div className="flex items-center gap-4 rounded-lg p-3 transition-colors hover:bg-gray-50">
                    <div className="flex-shrink-0">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100">
                        <IconCheck className="h-4 w-4 text-blue-600" />
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="font-medium text-gray-900">
                        Project created
                      </p>
                      <div className="mt-1 flex items-center gap-3">
                        <span className="text-sm text-gray-500">
                          {projectData?.createdAt
                            ? new Date(
                                projectData.createdAt,
                              ).toLocaleDateString()
                            : "Recently"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {projectData?.status && (
                  <div className="group">
                    <Separator className="ml-10" />
                    <div className="flex items-center gap-4 rounded-lg p-3 transition-colors hover:bg-gray-50">
                      <div className="flex-shrink-0">
                        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
                          <IconCheck className="h-4 w-4 text-green-600" />
                        </div>
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-gray-900">
                          Status set to &ldquo;{projectData.status}&rdquo;
                        </p>
                        <div className="mt-1 flex items-center gap-3">
                          <span className="text-sm text-gray-500">
                            {projectData?.updatedAt
                              ? new Date(
                                  projectData.updatedAt,
                                ).toLocaleDateString()
                              : "Recently"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {!projectData?.status && (
                  <div className="py-8 text-center">
                    <p className="text-sm text-gray-500 italic">
                      No recent activity
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Documents */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gray-100 p-2">
                    <IconFile className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Recent Documents
                    </h3>
                    <p className="text-sm text-gray-500">
                      Project files and attachments
                    </p>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  leftIcon={<IconPlus size={16} />}
                >
                  Upload File
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Placeholder documents - TODO: Replace with actual document queries */}
                <div className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50">
                  <div className="flex-shrink-0">
                    <div className="rounded-lg bg-blue-50 p-2">
                      <IconFileText className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-gray-900">
                      Project Proposal.pdf
                    </p>
                    <p className="text-sm text-gray-500">
                      Uploaded 2 days ago • 2.4 MB
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={<IconEye size={16} />}
                  />
                </div>

                <div className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50">
                  <div className="flex-shrink-0">
                    <div className="rounded-lg bg-green-50 p-2">
                      <IconFileText className="h-4 w-4 text-green-600" />
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-gray-900">
                      Requirements.docx
                    </p>
                    <p className="text-sm text-gray-500">
                      Uploaded 1 week ago • 1.8 MB
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={<IconEye size={16} />}
                  />
                </div>

                <div className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50">
                  <div className="flex-shrink-0">
                    <div className="rounded-lg bg-purple-50 p-2">
                      <IconFile className="h-4 w-4 text-purple-600" />
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-gray-900">
                      Design Mockups.zip
                    </p>
                    <p className="text-sm text-gray-500">
                      Uploaded 2 weeks ago • 12.5 MB
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={<IconEye size={16} />}
                  />
                </div>

                {/* Empty state for when there are no documents */}
                {false && (
                  <div className="py-8 text-center">
                    <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100">
                      <IconFile className="h-6 w-6 text-gray-400" />
                    </div>
                    <p className="text-sm font-medium text-gray-900">
                      No documents yet
                    </p>
                    <p className="text-sm text-gray-500">
                      Upload files related to this project
                    </p>
                    <Button
                      size="sm"
                      variant="outline"
                      className="mt-3"
                      leftIcon={<IconPlus size={16} />}
                    >
                      Upload File
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Right Side */}
        <div className="space-y-8">
          {/* Client Information */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gray-100 p-2">
                    <IconUser className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Client
                    </h3>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  leftIcon={<IconPencil size={16} />}
                />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <p className="font-semibold text-gray-900">
                    {getFullName(project?.data?.contact)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {project?.data?.contact?.email}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1"
                    href={`mailto:${project?.data?.contact?.email}`}
                    leftIcon={<IconMail size={16} />}
                  >
                    Email
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1"
                    href={`/contacts/${project?.data?.contact?.id}`}
                    leftIcon={<IconEye size={16} />}
                  >
                    View
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Details */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gray-100 p-2">
                    <IconCalendarDue className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Project Details
                    </h3>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  leftIcon={<IconPencil size={16} />}
                  onClick={editDetailsModeHandler.open}
                />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {projectData?.description && (
                  <div>
                    <label className="mb-2 block text-sm font-medium text-gray-700">
                      Description
                    </label>
                    <div className="rounded-lg border bg-gray-50 px-3 py-2">
                      <p className="text-sm text-gray-900">
                        {projectData.description}
                      </p>
                    </div>
                  </div>
                )}

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    Project Status
                  </label>
                  <div className="rounded-lg border bg-gray-50 px-3 py-2">
                    <span className="text-sm font-medium text-gray-900 capitalize">
                      {projectData?.status || "Not set"}
                    </span>
                  </div>
                </div>

                <DatePicker
                  label="End Date"
                  value={projectEndDate}
                  variant="secondary"
                  className="w-full"
                  onChange={handleEndDateUpdate}
                />

                {projectData?.createdAt && (
                  <div>
                    <label className="mb-2 block text-sm font-medium text-gray-700">
                      Created
                    </label>
                    <div className="rounded-lg border bg-gray-50 px-3 py-2">
                      <span className="text-sm text-gray-900">
                        {new Date(projectData.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                )}

                {daysRemaining !== null && (
                  <div>
                    <label className="mb-2 block text-sm font-medium text-gray-700">
                      Days Remaining
                    </label>
                    <div className="rounded-lg border bg-gray-50 px-3 py-2">
                      <span
                        className={`text-sm font-medium ${
                          daysRemaining <= 0
                            ? "text-red-600"
                            : daysRemaining <= 7
                              ? "text-amber-600"
                              : "text-gray-900"
                        }`}
                      >
                        {daysRemaining > 0
                          ? `${daysRemaining} days`
                          : "Overdue"}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-gray-100 p-2">
                  <IconFile className="h-5 w-5 text-gray-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Quick Actions
                  </h3>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                leftIcon={<IconFileText size={18} />}
              >
                Create Proposal
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                href={`/invoices/create?projectId=${projectId}&contactId=${projectData?.contactId}`}
                leftIcon={<IconReceiptDollar size={18} />}
                disabled={!projectData?.contactId}
              >
                Generate Invoice
              </Button>
              {projectData?.contactId && (
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  href={`/contacts/${projectData.contactId}`}
                  leftIcon={<IconUser size={18} />}
                >
                  View Contact
                </Button>
              )}
              <Button
                variant="outline"
                className="w-full justify-start"
                leftIcon={<IconPencil size={18} />}
                onClick={editDetailsModeHandler.open}
              >
                Edit Project
              </Button>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-gray-100 p-2">
                    <IconPencil className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      Notes
                    </h3>
                  </div>
                </div>
                {!notesEditMode && (
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={<IconPencil size={16} />}
                    onClick={() => setNotesEditMode(true)}
                  />
                )}
              </div>
            </CardHeader>
            <CardContent>
              {notesEditMode ? (
                <div className="space-y-4">
                  <TextareaAutosize
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="w-full rounded-lg border border-gray-200 p-3 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
                    placeholder="Add your project notes here..."
                    minRows={3}
                  />
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={handleNotesUpdate}
                      loading={updateMutation.isPending}
                    >
                      Save
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setNotesEditMode(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div>
                  {notes ? (
                    <p className="text-sm leading-relaxed text-gray-700">
                      {notes}
                    </p>
                  ) : (
                    <p className="text-sm text-gray-500 italic">
                      No notes added yet.
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {project?.data && (
        <ProjectEditDialog
          open={editDetailsMode}
          onClose={editDetailsModeHandler.close}
          project={project.data}
        />
      )}
    </div>
  );
}
