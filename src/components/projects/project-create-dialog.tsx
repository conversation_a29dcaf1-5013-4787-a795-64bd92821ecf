import { Autocomplete } from "@/components/ui/autocomplete";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useContacts } from "@/queries/contact.queries";
import { useProjectAddMutation } from "@/queries/project.queries";
import { projectCreateSchema } from "@/schemas/project.schemas";
import { type ProjectCreateInput } from "@/types/project.types";
import { getFullName } from "@/utils/get-full-name";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMemo } from "react";
import { Controller, useForm } from "react-hook-form";

interface Props extends DialogProps {
  onClose: () => void;
}

export function ProjectCreateDialog({ open, onClose }: Props) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ProjectCreateInput>({
    resolver: zodResolver(projectCreateSchema),
  });

  const contacts = useContacts();

  const options = useMemo(() => {
    const clients = contacts?.data;
    return clients?.data?.map((client) => ({
      label: `${getFullName(client)} - ${client.email}`,
      value: client.id,
    }));
  }, [contacts.data]);

  const onOptionChange = (option: unknown) => {
    const contactId = (option as { value: string }).value;
    return contactId;
  };

  const closeModal = () => {
    reset();
    onClose();
  };

  const handleFormSubmit = useProjectAddMutation();

  async function onSubmit(data: ProjectCreateInput) {
    await handleFormSubmit.mutateAsync(data);
    closeModal();
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Create a new project</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-3">
          <div className="space-y-6">
            <Input
              label="Project name"
              {...register("name")}
              allowAutoComplete={false}
              error={errors.name !== undefined}
              errorMessage={errors?.name?.message}
            />
            <Textarea
              label="Description"
              {...register("description")}
              rows={4}
            />
            <Controller
              control={control}
              name="contactId"
              rules={{ required: false }}
              render={({ field: { onChange } }) => (
                <Autocomplete
                  label="Client"
                  placeholder="Select a client"
                  options={options}
                  onChange={(option) => onChange(onOptionChange(option))}
                />
              )}
            />
            <Controller
              control={control}
              name="endDate"
              rules={{ required: false }}
              render={({ field: { onChange } }) => (
                <DatePicker
                  label="End date"
                  onChange={(date) => {
                    console.log(date);
                    onChange(date);
                  }}
                  useReactHookForm
                  className="w-full"
                  classNames={{
                    button: "w-full",
                  }}
                />
              )}
            />
          </div>

          <DialogFooter className="mt-7">
            <Button variant="outline" onClick={closeModal} type="button">
              Close
            </Button>
            <Button loading={isSubmitting} type="submit">
              Create project
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
