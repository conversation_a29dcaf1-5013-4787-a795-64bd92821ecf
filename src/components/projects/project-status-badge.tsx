import { Badge, type BadgeVariants } from "@/components/ui/badge";
import { type ProjectStatus } from "@/types/project.types";

const colors: Record<ProjectStatus, { color: BadgeVariants }> = {
  planned: { color: "blue" },
  paused: { color: "gray" },
  completed: { color: "green" },
  "in progress": { color: "yellow" },
};

interface Props {
  status: ProjectStatus;
}

export function ProjectStatusBadge({ status }: Props) {
  return (
    <Badge variant={colors[status]?.color || "gray"} className="capitalize">
      {status}
    </Badge>
  );
}
