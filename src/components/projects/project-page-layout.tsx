"use client";

import { ProjectEditDialog } from "@/components/projects/project-edit-dialog";
import { ProjectPageActionsMenu } from "@/components/projects/project-page-actions-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { NavButton } from "@/components/ui/nav-button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDialog } from "@/hooks/use-dialog";
import {
  useProjectById,
  useProjectUpdateMutation,
} from "@/queries/project.queries";
import { getInitials } from "@/utils/get-initials";
import { IconArrowLeft, IconPencil } from "@tabler/icons-react";
import { isEmpty } from "radash";

const projectNavLinks = [
  { label: "Overview", href: (id: string) => `/projects/${id}` },
  { label: "Tasks", href: (id: string) => `/projects/${id}/tasks` },
  { label: "Invoices", href: (id: string) => `/projects/${id}/invoices` },
  { label: "Proposals", href: (id: string) => `/projects/${id}/proposals` },
];

interface Props {
  children: React.ReactNode;
  projectId: string;
}

export function ProjectPageLayout({ children, projectId }: Props) {
  const [editMode, editModeHandlers] = useDialog();

  const project = useProjectById(projectId);

  const updateMutation = useProjectUpdateMutation();

  const handleStatusUpdate = async (status: string) => {
    return await updateMutation.mutateAsync({
      projectId,
      status,
    });
  };

  return (
    <div>
      {!project.isLoading && (
        <div>
          <div className="border-b border-gray-200 px-6 py-4">
            <Button
              href="/projects"
              leftIcon={<IconArrowLeft size={16} />}
              variant="ghost"
              size="sm"
            >
              Back to projects
            </Button>
          </div>

          <div className="border-b border-gray-200 px-6 py-6">
            <div className="flex items-center justify-between gap-x-8">
              <div>
                <div className="flex items-center space-x-5">
                  <Avatar className="h-16 w-16 shadow-sm ring-1 ring-gray-100">
                    <AvatarFallback className="bg-gradient-to-br from-gray-700 to-gray-900 text-lg font-semibold text-white">
                      {getInitials(project?.data?.name, 1)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="">
                    <div className="flex items-center gap-x-3">
                      <h2 className="text-2xl font-semibold tracking-tight text-gray-900">
                        {project?.data?.name}
                      </h2>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex space-x-3">
                <Select
                  value={project?.data?.status}
                  onValueChange={handleStatusUpdate}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Select a status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="planned">
                        <Badge className="w-[100px]" variant="blue">
                          Planned
                        </Badge>
                      </SelectItem>
                      <SelectItem value="in progress">
                        <Badge className="w-[100px]" variant="yellow">
                          In progress
                        </Badge>
                      </SelectItem>
                      <SelectItem value="completed">
                        <Badge className="w-[100px]" variant="green">
                          Completed
                        </Badge>
                      </SelectItem>
                      <SelectItem value="paused">
                        <Badge className="w-[100px]" variant="gray">
                          Paused
                        </Badge>
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <Button
                  variant="secondary"
                  leftIcon={<IconPencil size={16} />}
                  onClick={editModeHandlers.open}
                >
                  Edit
                </Button>
                {project?.data && (
                  <ProjectPageActionsMenu
                    id={projectId}
                    project={project.data}
                  />
                )}
              </div>
            </div>

            <div className="space-x-2 pl-[64px]">
              {!isEmpty(project?.data?.description) && (
                <p className="text-gray-500">{project?.data?.description}</p>
              )}
            </div>
          </div>

          <main>
            <div className="border-b border-gray-200 px-6 py-4">
              <div className="space-x-2">
                {projectNavLinks.map((nav) => (
                  <NavButton key={nav.label} href={nav.href(projectId)}>
                    {nav.label}
                  </NavButton>
                ))}
              </div>
            </div>

            <div className="px-6 py-8">{children}</div>
          </main>

          {project?.data && (
            <ProjectEditDialog
              open={editMode}
              onClose={editModeHandlers.close}
              project={project.data}
            />
          )}
        </div>
      )}

      {project.isLoading && (
        <div className="flex min-h-screen items-center justify-center">
          <Loader />
        </div>
      )}
    </div>
  );
}
