"use client";

import { ProjectEditDialog } from "@/components/projects/project-edit-dialog";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { DatePicker } from "@/components/ui/date-picker";
import { Label } from "@/components/ui/label";
import { useDialog } from "@/hooks/use-dialog";
import {
  useProjectById,
  useProjectUpdateMutation,
} from "@/queries/project.queries";
import { formatCurrency } from "@/utils/format-currency";
import { getFullName } from "@/utils/get-full-name";
import {
  IconChecklist,
  IconEye,
  IconFile,
  IconFileText,
  IconHistory,
  IconInfoCircle,
  IconMail,
  IconNotes,
  IconPencil,
  IconPlus,
  IconReceiptDollar,
} from "@tabler/icons-react";
import { useEffect, useState } from "react";
import TextareaAutosize from "react-textarea-autosize";

interface Props {
  projectId: string;
}

export function ProjectOverviewView({ projectId }: Props) {
  const [notes, setNotes] = useState("");
  const [notesEditMode, setNotesEditMode] = useState(false);
  const [projectEndDate, setProjectEndDate] = useState<Date | null>(null);
  const [editDetailsMode, editDetailsModeHandler] = useDialog();

  const project = useProjectById(projectId);

  const updateMutation = useProjectUpdateMutation();

  useEffect(() => {
    if (project?.data?.notes) {
      setNotes(project?.data?.notes);
    }
    if (project?.data?.endDate) {
      setProjectEndDate(new Date(project?.data?.endDate));
    }
  }, [project?.data?.notes, project?.data?.endDate]);

  const handleEndDateUpdate = async (date: Date | undefined) => {
    if (!date) return;
    setProjectEndDate(date);
    return await updateMutation.mutateAsync({
      projectId,
      endDate: date.toISOString(),
    });
  };

  const handleNotesUpdate = async () => {
    await updateMutation.mutateAsync({
      projectId,
      notes,
    });
    setNotesEditMode(false);
  };

  console.log("project?.data?.endDate: ", project?.data?.endDate);

  // Tasks array for the task list
  const tasks = [
    {
      id: "1",
      label: "Send the invoice",
      due: "Due in 2 days",
      dueClass: "text-red-500",
      completed: false,
    },
    {
      id: "2",
      label: "Follow up on proposal",
      due: "Due today",
      dueClass: "text-orange-500",
      completed: false,
    },
    {
      id: "3",
      label: "Complete project scope",
      due: "Completed yesterday",
      dueClass: "text-gray-400",
      completed: true,
    },
  ];

  return (
    <div className="mx-auto 2xl:max-w-7xl">
      <div className="grid grid-cols-2 gap-x-6">
        <div className="space-y-6">
          <div className="space-y-2">
            <div>
              <Card>
                <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3">
                  <h2 className="flex items-center gap-2 text-base font-medium text-gray-900">
                    <IconReceiptDollar size={20} />
                    Invoices
                  </h2>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-gray-600"
                  >
                    <IconPlus size={18} />
                    <span className="sr-only">Add invoice</span>
                  </Button>
                </div>
                <div className="grid grid-cols-2 gap-6 px-4 py-4 lg:grid-cols-4">
                  <div className="text-center">
                    <p className="mb-2 text-sm font-medium text-gray-600">
                      Issued
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(0)}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="mb-2 text-sm font-medium text-gray-600">
                      Outstanding
                    </p>
                    <p className="text-2xl font-bold text-amber-600">
                      {formatCurrency(0)}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="mb-2 text-sm font-medium text-gray-600">
                      Overdue
                    </p>
                    <p className="text-2xl font-bold text-red-600">
                      {formatCurrency(0)}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="mb-2 text-sm font-medium text-gray-600">
                      Paid
                    </p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(0)}
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>

          <div className="mt-6 space-y-2">
            <div>
              <Card>
                <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3">
                  <h2 className="flex items-center gap-2 text-base font-medium text-gray-900">
                    <IconChecklist size={20} />
                    Tasks
                  </h2>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-gray-600"
                  >
                    <IconPlus size={18} />
                    <span className="sr-only">Add task</span>
                  </Button>
                </div>
                <div className="px-4 py-4">
                  <div className="space-y-0">
                    {tasks.map((task) => (
                      <div
                        key={task.id}
                        className="group flex items-center gap-3 border-b border-gray-200 px-3 py-2 transition-colors last:border-b-0 hover:bg-gray-50/80"
                      >
                        <button
                          className={`flex h-5 w-5 items-center justify-center rounded-full border-2 ${task.completed ? "border-green-500 bg-green-500" : "border-gray-300 bg-white"} transition-colors hover:border-gray-400`}
                        >
                          {task.completed && (
                            <svg
                              className="h-2.5 w-2.5 text-white"
                              fill="currentColor"
                              viewBox="0 0 8 8"
                            >
                              <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z" />
                            </svg>
                          )}
                        </button>
                        <div className="min-w-0 flex-1">
                          <p
                            className={`text-sm font-medium ${task.completed ? "text-gray-500 line-through" : "text-gray-900"}`}
                          >
                            {task.label}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className={`text-sm ${task.dueClass}`}>
                            {task.due}
                          </p>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-8 opacity-0 transition-opacity group-hover:opacity-100"
                          >
                            <span className="sr-only">View</span>
                            <IconEye size={16} />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            </div>
          </div>

          <div className="space-y-2">
            <Card>
              <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3">
                <h2 className="flex items-center gap-2 text-base font-medium text-gray-900">
                  <IconNotes size={20} />
                  Notes
                </h2>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setNotesEditMode(true)}
                >
                  <IconPencil size={16} />
                </Button>
              </div>
              <div className="px-4 py-4">
                {notesEditMode && (
                  <div className="space-y-4">
                    <TextareaAutosize
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="block w-full rounded-lg border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-primary focus:ring-inset sm:text-sm sm:leading-6"
                      placeholder="Add a note (this is only visible to you)..."
                      minRows={4}
                    />
                    <div className="space-x-2">
                      <Button
                        onClick={handleNotesUpdate}
                        loading={updateMutation.isPending}
                      >
                        Save changes
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setNotesEditMode(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
                {!notesEditMode && !notes && (
                  <p className="text-sm text-gray-500">No notes yet.</p>
                )}
                {!notesEditMode && notes && (
                  <p className="text-sm text-gray-700">{notes}</p>
                )}
              </div>
            </Card>
          </div>
        </div>

        <div className="space-y-6">
          <div className="space-y-2">
            <Card>
              <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3">
                <h2 className="flex items-center gap-2 text-base font-medium text-gray-900">
                  <IconInfoCircle size={20} />
                  Project details
                </h2>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={editDetailsModeHandler.open}
                >
                  <IconPencil size={16} />
                </Button>
              </div>
              <div className="space-y-4 px-4 py-4">
                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-900">
                    Client
                  </Label>
                  <Card className="flex items-center justify-between p-4">
                    <div>
                      <p className="text-sm font-semibold">
                        {getFullName(project?.data?.contact)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {project?.data?.contact?.email}
                      </p>
                    </div>
                    <div className="flex items-center gap-x-2">
                      <Button
                        href={`mailto:${project?.data?.contact?.email}`}
                        leftIcon={<IconMail size={16} />}
                        size="sm"
                        variant={"secondary"}
                      >
                        Message
                      </Button>
                      <Button
                        href={`/contacts/${project?.data?.contact?.id}`}
                        leftIcon={<IconEye size={16} />}
                        size="sm"
                        variant={"secondary"}
                      >
                        View
                      </Button>
                    </div>
                  </Card>
                </div>

                <div>
                  <div className="space-y-3">
                    <DatePicker
                      label="End date"
                      value={projectEndDate}
                      variant={"secondary"}
                      className="w-auto"
                      onChange={handleEndDateUpdate}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <div className="space-y-2">
            <Card>
              <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3">
                <h2 className="flex items-center gap-2 text-base font-medium text-gray-900">
                  <IconFile size={20} />
                  Recent documents
                </h2>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <IconPlus size={16} />
                </Button>
              </div>
              <div className="space-y-3 px-4 py-4">
                <Button
                  className="w-full justify-start border-dashed py-6 text-sm font-normal"
                  variant="outline"
                  leftIcon={<IconFileText size={20} />}
                >
                  Create proposal
                </Button>
                <Button
                  className="w-full justify-start border-dashed py-6 text-sm font-normal"
                  variant="outline"
                  leftIcon={<IconReceiptDollar size={20} />}
                >
                  Create invoice
                </Button>
              </div>
            </Card>
          </div>

          <div className="space-y-2">
            <Card>
              <div className="border-b border-gray-200 px-4 py-3">
                <h2 className="flex items-center gap-2 text-base font-medium text-gray-900">
                  <IconHistory size={20} />
                  Activity
                </h2>
              </div>
              <div className="px-4 py-4">
                <p className="text-sm text-gray-500">No activity yet.</p>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {project?.data && (
        <ProjectEditDialog
          open={editDetailsMode}
          onClose={editDetailsModeHandler.close}
          project={project.data}
        />
      )}
    </div>
  );
}
