import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useStopImpersonating } from "@/queries/admin-user.queries";
import { useUserCurrentSession } from "@/queries/user.queries";
import { useRouter } from "next/navigation";

export function ImpersonatedBy() {
  const router = useRouter();
  const session = useUserCurrentSession();
  const stopImpersonatingMutation = useStopImpersonating();

  if (session.isLoading || !session.data || !session.data.session) return null;

  // If not impersonated, don't show anything
  if (!session.data.session.impersonatedBy) return null;

  const handleStopImpersonating = async () => {
    await stopImpersonatingMutation.mutateAsync({});
    router.push("/admin/users");
  };

  return (
    <Alert className="relative border-yellow-300 bg-yellow-50 text-yellow-900">
      <AlertTitle className="text-base">Impersonation Active</AlertTitle>
      <AlertDescription>
        You are currently impersonating another user.
        <div className="mt-2">
          <Button
            variant="destructive"
            size="sm"
            loading={stopImpersonatingMutation.isPending}
            onClick={handleStopImpersonating}
          >
            Stop Impersonating
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
