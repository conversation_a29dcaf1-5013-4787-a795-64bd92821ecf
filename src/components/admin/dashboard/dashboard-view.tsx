"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  PageWrapper,
} from "@/components/ui/page-structure";
import { StatCard } from "@/components/ui/stat-card";
import { useAdminUserStats } from "@/queries/admin-stats.queries";
import {
  IconActivity,
  IconCalendar,
  IconShield,
  IconShieldExclamation,
  IconUserCheck,
  IconUserPlus,
  IconUsers,
  IconUserX,
} from "@tabler/icons-react";

export function AdminDashboardView() {
  const { data, isLoading, error } = useAdminUserStats();
  let errorMessage: string | null = null;
  if (error) {
    errorMessage =
      error instanceof Error
        ? error.message
        : typeof error === "string"
          ? error
          : "Error loading stats";
  }
  return (
    <PageWrapper>
      <PageTitle>Dashboard</PageTitle>
      <PageContent>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          <StatCard
            title="Total Users"
            value={data?.totalUsers ?? 0}
            description="All registered users"
            icon={IconUsers}
            isLoading={isLoading}
          />
          <StatCard
            title="New Users"
            value={data?.newUsersThisMonth ?? 0}
            description="New users in last 30 days"
            icon={IconUserPlus}
            isLoading={isLoading}
          />
          <StatCard
            title="Monthly Active"
            value={data?.activeUsers ?? 0}
            description="Active in last 30 days"
            icon={IconUserCheck}
            isLoading={isLoading}
          />
          <StatCard
            title="Daily Active"
            value={data?.dailyActiveUsers ?? 0}
            description="Active in last 24 hours"
            icon={IconActivity}
            isLoading={isLoading}
          />
          <StatCard
            title="Weekly Active"
            value={data?.weeklyActiveUsers ?? 0}
            description="Active in last 7 days"
            icon={IconCalendar}
            isLoading={isLoading}
          />
          <StatCard
            title="Unverified Users"
            value={data?.unverifiedUsers ?? 0}
            description="Pending email verification"
            icon={IconShieldExclamation}
            isLoading={isLoading}
          />
          <StatCard
            title="Passkey Users"
            value={data?.passkeyUsers ?? 0}
            description="Users with passkeys enabled"
            icon={IconShield}
            isLoading={isLoading}
          />
          <StatCard
            title="Banned Users"
            value={data?.bannedUsers ?? 0}
            description="Currently banned accounts"
            icon={IconUserX}
            isLoading={isLoading}
          />
        </div>

        {errorMessage && (
          <div className="mt-4 text-red-500">{errorMessage}</div>
        )}
      </PageContent>
    </PageWrapper>
  );
}
