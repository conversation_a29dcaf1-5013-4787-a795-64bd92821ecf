"use client";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  IconBrandStripe,
  IconCreditCard,
  IconHome,
  IconUsers,
} from "@tabler/icons-react";
import Link from "next/link";
import { usePathname, useSelectedLayoutSegment } from "next/navigation";

const menuItems = [
  {
    path: "/admin",
    name: "Dashboard",
    icon: <IconHome size={18} />,
    segment: "dashboard",
  },
  {
    path: "/admin/users",
    name: "Users",
    icon: <IconUsers size={18} />,
    segment: "users",
  },
  {
    path: "/admin/subscriptions",
    name: "Subscriptions",
    icon: <IconCreditCard size={18} />,
    segment: "subscriptions",
  },
];

export function AdminNav() {
  const segment = useSelectedLayoutSegment();
  const pathname = usePathname();

  function isActive({
    path,
    segmentName,
  }: {
    path: string;
    segmentName: string;
  }) {
    return segment === segmentName || pathname === path;
  }

  return (
    <SidebarGroup>
      <SidebarMenu>
        {menuItems.map((item) => (
          <Link key={item.path} href={item.path}>
            <SidebarMenuItem>
              <SidebarMenuButton
                isActive={isActive({
                  path: item.path,
                  segmentName: item.segment,
                })}
                className="relative h-9 rounded-md border border-sidebar px-3 py-2 text-sm font-medium transition-all duration-200 ease-in-out data-[active=true]:border data-[active=true]:border-gray-200 data-[active=true]:bg-white data-[active=true]:shadow-xs"
              >
                {item.icon}
                <span>{item.name}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </Link>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
