"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "@/lib/zod";
import {
  useSetUserPassword,
  useSetUserRole,
} from "@/queries/admin-user.queries";
import type { User } from "@/types/admin-user.types";
import { zodResolver } from "@hookform/resolvers/zod";
import * as React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface EditUserDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface EditUserForm {
  name: string;
  email: string;
  role: "admin" | "user";
  newPassword?: string;
  confirmPassword?: string;
}

// Zod schema for form validation
const editUserSchema = z
  .object({
    name: z.string(),
    email: z.string().email(),
    role: z.enum(["admin", "user"]),
    newPassword: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .optional()
      .or(z.literal("")),
    confirmPassword: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.newPassword && data.newPassword !== "") {
        return data.confirmPassword === data.newPassword;
      }
      return true;
    },
    {
      message: "Passwords do not match",
      path: ["confirmPassword"],
    },
  );

// Helper function to get display role
const getDisplayRole = (role: User["role"]): "admin" | "user" => {
  if (!role) return "user";
  if (typeof role === "string") return role;
  if (Array.isArray(role)) {
    return (role as string[]).includes("admin") ? "admin" : "user";
  }
  return "user";
};

export function EditUserDialog({
  user,
  open,
  onOpenChange,
}: EditUserDialogProps) {
  const setRoleMutation = useSetUserRole();
  const setPasswordMutation = useSetUserPassword();

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(editUserSchema),
  });

  const newPassword = watch("newPassword");

  // Reset form when user changes
  React.useEffect(() => {
    if (user) {
      reset({
        name: user.name || "",
        email: user.email || "",
        role: getDisplayRole(user.role),
        newPassword: "",
        confirmPassword: "",
      });
    }
  }, [user, reset]);

  const onSubmit = async (data: EditUserForm) => {
    if (!user) return;

    try {
      const promises = [];

      // Update role if it changed
      const currentRole = getDisplayRole(user.role);
      if (data.role !== currentRole) {
        promises.push(
          setRoleMutation.mutateAsync({
            userId: user.id,
            role: data.role,
          }),
        );
      }

      // Update password if provided
      if (data.newPassword && data.newPassword.trim()) {
        if (data.newPassword !== data.confirmPassword) {
          toast.error("Passwords do not match");
          return;
        }
        promises.push(
          setPasswordMutation.mutateAsync({
            userId: user.id,
            newPassword: data.newPassword,
          }),
        );
      }

      if (promises.length > 0) {
        await Promise.all(promises);
        toast.success("User updated successfully");
      } else {
        toast.info("No changes were made");
      }

      reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error updating user:", error);
      toast.error("Failed to update user");
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      reset();
    }
    onOpenChange(newOpen);
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit user</DialogTitle>
          <DialogDescription className="mt-2">
            Update user information and settings.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Input
                id="name"
                label="Name"
                placeholder="Enter full name"
                disabled
                {...register("name")}
                description="Name cannot be changed from admin panel"
              />
            </div>

            <div className="grid gap-2">
              <Input
                id="email"
                label="Email"
                type="email"
                placeholder="Enter email address"
                disabled
                {...register("email")}
                description="Email cannot be changed from admin panel"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="role" className="text-sm font-medium">
                Role
              </label>
              <Select
                onValueChange={(value: "admin" | "user") =>
                  setValue("role", value)
                }
                defaultValue={getDisplayRole(user.role)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="border-t pt-4">
              <h4 className="mb-4 text-sm font-medium">
                Change Password (Optional)
              </h4>

              <div className="grid gap-2">
                <Input
                  id="newPassword"
                  label="New Password"
                  type="password"
                  placeholder="Enter new password (leave empty to keep current)"
                  error={!!errors.newPassword}
                  errorMessage={errors.newPassword?.message}
                  {...register("newPassword")}
                />
              </div>

              {newPassword && (
                <div className="mt-2 grid gap-2">
                  <Input
                    id="confirmPassword"
                    label="Confirm New Password"
                    type="password"
                    placeholder="Confirm new password"
                    error={!!errors.confirmPassword}
                    errorMessage={errors.confirmPassword?.message}
                    {...register("confirmPassword")}
                  />
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update User"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
