"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "@/lib/zod";
import { useCreateUser } from "@/queries/admin-user.queries";
import type { CreateUserParams } from "@/types/admin-user.types";
import { zodResolver } from "@hookform/resolvers/zod";
import * as React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface CreateUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface CreateUserForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: "admin" | "user";
}

export function CreateUserDialog({
  open,
  onOpenChange,
}: CreateUserDialogProps) {
  const createUserMutation = useCreateUser();

  // Zod schema for form validation
  const createUserSchema = z
    .object({
      name: z.string().min(1, "Name is required"),
      email: z.string().email("Please enter a valid email address"),
      password: z.string().min(8, "Password must be at least 8 characters"),
      confirmPassword: z.string().min(1, "Please confirm your password"),
      role: z.enum(["admin", "user"]),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords do not match",
      path: ["confirmPassword"],
    });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CreateUserForm>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      role: "user",
    },
  });

  const onSubmit = async (data: CreateUserForm) => {
    try {
      const params: CreateUserParams = {
        name: data.name,
        email: data.email,
        password: data.password,
        role: data.role,
      };

      await createUserMutation.mutateAsync(params);
      toast.success("User created successfully");
      reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error creating user:", error);
      toast.error("Failed to create user");
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create a new user</DialogTitle>
          <DialogDescription className="mt-2">
            Add a new user to the system. They will receive an email with their
            account details.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Input
                id="name"
                label="Name"
                placeholder="Enter full name"
                error={!!errors.name}
                errorMessage={errors.name?.message}
                {...register("name")}
              />
            </div>

            <div className="grid gap-2">
              <Input
                id="email"
                label="Email"
                type="email"
                placeholder="Enter email address"
                error={!!errors.email}
                errorMessage={errors.email?.message}
                {...register("email")}
              />
            </div>

            <div className="grid gap-2">
              <Input
                id="password"
                label="Password"
                type="password"
                placeholder="Enter password"
                error={!!errors.password}
                errorMessage={errors.password?.message}
                {...register("password")}
              />
            </div>

            <div className="grid gap-2">
              <Input
                id="confirmPassword"
                label="Confirm Password"
                type="password"
                placeholder="Confirm password"
                error={!!errors.confirmPassword}
                errorMessage={errors.confirmPassword?.message}
                {...register("confirmPassword")}
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="role" className="text-sm font-medium">
                Role
              </label>
              <Select
                onValueChange={(value: "admin" | "user") =>
                  setValue("role", value)
                }
                defaultValue="user"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create User"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
