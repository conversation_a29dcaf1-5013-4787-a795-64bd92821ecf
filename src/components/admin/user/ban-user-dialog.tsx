"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { z } from "@/lib/zod";
import { useBanUser } from "@/queries/admin-user.queries";
import type { User } from "@/types/admin-user.types";
import { zodResolver } from "@hookform/resolvers/zod";
import * as React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface BanUserDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface BanUserForm {
  banReason: string;
  banDuration: string;
  customDays?: number;
}

const BAN_DURATIONS = [
  { value: "1", label: "1 Day" },
  { value: "7", label: "7 Days" },
  { value: "30", label: "30 Days" },
  { value: "90", label: "90 Days" },
  { value: "365", label: "1 Year" },
  { value: "permanent", label: "Permanent" },
  { value: "custom", label: "Custom" },
];

export function BanUserDialog({
  user,
  open,
  onOpenChange,
}: BanUserDialogProps) {
  const banUserMutation = useBanUser();

  // Zod schema for form validation
  const banUserSchema = z
    .object({
      banReason: z.string().min(1, "Please provide a reason for the ban"),
      banDuration: z.string(),
      customDays: z
        .number()
        .min(1, "Duration must be at least 1 day")
        .optional(),
    })
    .refine(
      (data) => {
        if (data.banDuration === "custom") {
          return typeof data.customDays === "number" && data.customDays >= 1;
        }
        return true;
      },
      {
        message: "Please enter the number of days",
        path: ["customDays"],
      },
    );

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<BanUserForm>({
    resolver: zodResolver(banUserSchema),
    defaultValues: {
      banReason: "",
      banDuration: "7",
      customDays: undefined,
    },
  });

  const banDuration = watch("banDuration");

  const onSubmit = async (data: BanUserForm) => {
    if (!user) return;

    try {
      let banExpiresIn: number | undefined;

      if (data.banDuration === "permanent") {
        banExpiresIn = undefined; // Permanent ban
      } else if (data.banDuration === "custom") {
        if (!data.customDays || data.customDays < 1) {
          toast.error("Please enter a valid number of days");
          return;
        }
        banExpiresIn = data.customDays * 24 * 60 * 60 * 1000; // Convert days to milliseconds
      } else {
        banExpiresIn = parseInt(data.banDuration) * 24 * 60 * 60 * 1000; // Convert days to milliseconds
      }

      await banUserMutation.mutateAsync({
        userId: user.id,
        banReason: data.banReason || "No reason provided",
        banExpiresIn,
      });

      toast.success(`User ${user.name || user.email} has been banned`);
      reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error banning user:", error);
      toast.error("Failed to ban user");
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      reset();
    }
    if (typeof onOpenChange === "function") {
      onOpenChange(newOpen);
    }
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Ban user</DialogTitle>
          <DialogDescription className="mt-2">
            Ban {user.name || user.email} from accessing the system.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Textarea
                id="banReason"
                label="Reason for Ban"
                placeholder="Enter the reason for banning this user..."
                rows={3}
                {...register("banReason")}
                error={!!errors.banReason}
                errorMessage={errors.banReason?.message}
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="banDuration" className="text-sm font-medium">
                Ban Duration
              </label>
              <Select
                onValueChange={(value) => setValue("banDuration", value)}
                defaultValue="7"
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select ban duration" />
                </SelectTrigger>
                <SelectContent>
                  {BAN_DURATIONS.map((duration) => (
                    <SelectItem key={duration.value} value={duration.value}>
                      {duration.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {banDuration === "custom" && (
              <div className="grid gap-2">
                <Input
                  id="customDays"
                  label="Custom Duration (Days)"
                  type="number"
                  min="1"
                  placeholder="Enter number of days"
                  error={!!errors.customDays}
                  errorMessage={errors.customDays?.message}
                  {...register("customDays", { valueAsNumber: true })}
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="destructive"
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              Ban User
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
