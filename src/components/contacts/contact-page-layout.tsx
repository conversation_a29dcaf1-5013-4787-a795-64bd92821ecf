"use client";

import { ContactEditDialog } from "@/components/contacts/contact-edit-dialog";
import { ContactPageActionsMenu } from "@/components/contacts/contact-page-actions-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { NavButton } from "@/components/ui/nav-button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useDialog } from "@/hooks/use-dialog";
import {
  useContactById,
  useContactUpdateMutation,
} from "@/queries/contact.queries";
import { type ContactType } from "@/types/contact.types";
import { formatPhoneNumber } from "@/utils/format-phone-number";
import { getFullName } from "@/utils/get-full-name";
import { getInitials } from "@/utils/get-initials";
import {
  IconArrowLeft,
  IconMail,
  IconPencil,
  IconPhone,
} from "@tabler/icons-react";

interface Props {
  children: React.ReactNode;
  contactId: string;
}

export default function ContactPageLayout({ children, contactId }: Props) {
  const [editMode, editModeHandlers] = useDialog();

  const contact = useContactById(contactId);

  const updateMutation = useContactUpdateMutation();

  const handleTypeUpdate = async (type: ContactType) => {
    return await updateMutation.mutateAsync({
      contactId,
      type,
    });
  };

  return (
    <div>
      {!contact.isLoading && (
        <div>
          <div className="border-b border-gray-200 px-6 py-4">
            <Button
              href="/contacts"
              leftIcon={<IconArrowLeft size={16} />}
              variant="ghost"
              size="sm"
            >
              Back to contacts
            </Button>
          </div>

          <div className="border-b border-gray-200 px-6 py-6">
            <div className="flex items-center justify-between gap-x-8">
              <div>
                <div className="flex items-center space-x-6">
                  <Avatar>
                    <AvatarFallback className="uppercase text-white">
                      {getInitials(getFullName(contact?.data), 2)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="">
                    <div className="flex items-center gap-x-6">
                      <h2 className="text-xl font-semibold">
                        {getFullName(contact?.data)}
                      </h2>
                      <div className="space-x-3">
                        <Button
                          href={`mailto:${contact?.data?.email}`}
                          variant="secondary"
                          leftIcon={<IconMail size={16} />}
                        >
                          Message
                        </Button>
                        {contact?.data?.phone && (
                          <Button
                            href={`tel:${formatPhoneNumber(contact?.data?.phone)}`}
                            variant="secondary"
                            leftIcon={<IconPhone size={16} />}
                          >
                            Call
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex space-x-3">
                <Select
                  value={contact?.data?.type}
                  onValueChange={handleTypeUpdate}
                >
                  <SelectTrigger className="w-[100px]">
                    <SelectValue placeholder="Select a type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="lead">
                        <Badge variant="blue">Lead</Badge>
                      </SelectItem>
                      <SelectItem value="client">
                        <Badge variant="green">Client</Badge>
                      </SelectItem>
                      <SelectItem value="other">
                        <Badge variant="gray">Other</Badge>
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <Button
                  variant="secondary"
                  leftIcon={<IconPencil size={16} />}
                  onClick={editModeHandlers.open}
                >
                  Edit
                </Button>
                {contact?.data && <ContactPageActionsMenu id={contactId} />}
              </div>
            </div>

            <div className="mt-2 hidden space-x-2 pl-[64px]">
              <Button
                href={`mailto:${contact?.data?.email}`}
                variant="secondary"
                leftIcon={<IconMail size={16} />}
              >
                Message
              </Button>
              {contact?.data?.phone && (
                <Button
                  href={`tel:${formatPhoneNumber(contact?.data?.phone)}`}
                  variant="secondary"
                  leftIcon={<IconPhone size={16} />}
                >
                  Call
                </Button>
              )}
            </div>
          </div>

          <main>
            {/* <div className="border-b border-gray-200 px-6 py-4">
              <div className="space-x-2">
                <NavButton href={`/contacts/${contactId}`}>Overview</NavButton>
                <NavButton href={`/contacts/${contactId}/projects`}>
                  Projects
                </NavButton>
                <NavButton href={`/contacts/${contactId}/invoices`}>
                  Invoices
                </NavButton>
                <NavButton href={`/contacts/${contactId}/proposals`}>
                  Proposals
                </NavButton>
                <NavButton href={`/contacts/${contactId}/tasks`}>
                  Tasks
                </NavButton>
              </div>
            </div> */}

            <div className="px-6 py-8">{children}</div>
          </main>

          {contact?.data && (
            <ContactEditDialog
              open={editMode}
              onClose={editModeHandlers.close}
              contact={contact.data}
            />
          )}
        </div>
      )}

      {contact.isLoading && (
        <div className="flex min-h-screen items-center justify-center">
          <Loader />
        </div>
      )}
    </div>
  );
}
