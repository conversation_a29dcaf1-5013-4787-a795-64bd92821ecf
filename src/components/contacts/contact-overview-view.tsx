"use client";

import { ContactEditDialog } from "@/components/contacts/contact-edit-dialog";
// import { ProjectActionsMenu } from "@/components/projects/project-actions-menu";
// import { ProjectStatusBadge } from "@/components/projects/project-status-badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useDialog } from "@/hooks/use-dialog";
import {
  useContactById,
  useContactUpdateMutation,
} from "@/queries/contact.queries";
import {
  useInfiniteProjectsByContactId,
  useProjectsByContactId,
} from "@/queries/project.queries";
import { type ProjectStatus } from "@/types/project.types";
import { formatCurrency } from "@/utils/format-currency";
import { formatPhoneNumber } from "@/utils/format-phone-number";
import { getFullName } from "@/utils/get-full-name";
import {
  IconBuilding,
  IconFileText,
  IconMail,
  IconMapPin,
  IconPencil,
  IconPhone,
  IconPlus,
  IconReceiptDollar,
  IconUser,
  IconWorld,
} from "@tabler/icons-react";
import Link from "next/link";
import { isEmpty } from "radash";
import { useEffect, useState } from "react";
import TextareaAutosize from "react-textarea-autosize";

interface Props {
  contactId: string;
}

export function ContactOverviewView({ contactId }: Props) {
  const [notes, setNotes] = useState("");
  const [notesEditMode, setNotesEditMode] = useState(false);
  const [editDetailsMode, editDetailsModeHandler] = useDialog();

  const contact = useContactById(contactId);
  const { data: projects } = useProjectsByContactId(contactId);

  const updateMutation = useContactUpdateMutation();

  useEffect(() => {
    if (contact?.data?.notes) {
      setNotes(contact?.data?.notes);
    }
  }, [contact?.data?.notes]);

  const handleNotesUpdate = async () => {
    await updateMutation.mutateAsync({
      contactId,
      notes,
    });
    setNotesEditMode(false);
  };

  return (
    <div className="mx-auto 2xl:max-w-7xl">
      <div className="grid grid-cols-2 gap-x-6">
        <div className="space-y-6">
          <div className="space-y-2">
            <div>
              <Card>
                <div className="border-b border-gray-200 px-4 py-2">
                  <h2 className="text-sm leading-7 font-semibold text-gray-900">
                    Invoices
                  </h2>
                </div>
                <div className="w-full items-center justify-between gap-x-4 px-6 py-7 lg:flex">
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Issued</p>
                    <p className="font-semibold">{formatCurrency(0)}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Outstanding</p>
                    <p className="font-semibold">{formatCurrency(0)}</p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Overdue</p>
                    <p className="font-semibold text-red-500">
                      {formatCurrency(0)}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Paid</p>
                    <p className="font-semibold text-green-600">
                      {formatCurrency(0)}
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          </div>

          <div className="space-y-2">
            <div>
              <Card>
                <div className="flex items-center justify-between border-b border-gray-200 px-4 py-2">
                  <h2 className="text-sm leading-7 font-semibold text-gray-900">
                    Tasks
                  </h2>
                  <Button variant="ghost" size="icon" className="h-7 w-7">
                    <IconPlus size={16} />
                  </Button>
                </div>
                <ul role="list" className="divide-y divide-gray-200 px-6">
                  <li className="flex items-center justify-between gap-x-6 py-4">
                    <div className="min-w-0">
                      <div className="flex items-start gap-x-3">
                        <p className="text-sm leading-6 font-medium text-gray-900">
                          Send the invoice
                        </p>
                        {/* <p
                          className={classNames(
                            statuses[project.status],
                            "mt-0.5 whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium ring-1 ring-inset",
                          )}
                        >
                          {project.status}
                        </p> */}
                      </div>
                      <div className="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                        {/* <p className="whitespace-nowrap">
                          Due on{" "}
                          <time dateTime={project.dueDateTime}>
                            {project.dueDate}
                          </time>
                        </p> */}
                      </div>
                    </div>
                    <div className="flex flex-none items-center gap-x-4">
                      <a
                        href={"/contacts"}
                        className="hidden rounded-md bg-white px-2.5 py-1.5 text-sm font-medium text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset hover:bg-gray-50 sm:block"
                      >
                        View task
                      </a>
                    </div>
                  </li>
                  <li className="flex items-center justify-between gap-x-6 py-4">
                    <div className="min-w-0">
                      <div className="flex items-start gap-x-3">
                        <p className="text-sm leading-6 font-medium text-gray-900">
                          Send the invoice
                        </p>
                        {/* <p
                          className={classNames(
                            statuses[project.status],
                            "mt-0.5 whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium ring-1 ring-inset",
                          )}
                        >
                          {project.status}
                        </p> */}
                      </div>
                      <div className="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                        {/* <p className="whitespace-nowrap">
                          Due on{" "}
                          <time dateTime={project.dueDateTime}>
                            {project.dueDate}
                          </time>
                        </p> */}
                      </div>
                    </div>
                    <div className="flex flex-none items-center gap-x-4">
                      <a
                        href={"/contacts"}
                        className="hidden rounded-md bg-white px-2.5 py-1.5 text-sm font-medium text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset hover:bg-gray-50 sm:block"
                      >
                        View task
                      </a>
                    </div>
                  </li>
                  <li className="flex items-center justify-between gap-x-6 py-4">
                    <div className="min-w-0">
                      <div className="flex items-start gap-x-3">
                        <p className="text-sm leading-6 font-medium text-gray-900">
                          Send the invoice
                        </p>
                        {/* <p
                          className={classNames(
                            statuses[project.status],
                            "mt-0.5 whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium ring-1 ring-inset",
                          )}
                        >
                          {project.status}
                        </p> */}
                      </div>
                      <div className="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                        {/* <p className="whitespace-nowrap">
                          Due on{" "}
                          <time dateTime={project.dueDateTime}>
                            {project.dueDate}
                          </time>
                        </p> */}
                      </div>
                    </div>
                    <div className="flex flex-none items-center gap-x-4">
                      <a
                        href={"/contacts"}
                        className="hidden rounded-md bg-white px-2.5 py-1.5 text-sm font-medium text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset hover:bg-gray-50 sm:block"
                      >
                        View task
                      </a>
                    </div>
                  </li>
                </ul>
              </Card>
            </div>
          </div>

          <div className="space-y-2">
            <div>
              <Card>
                <div className="flex items-center justify-between border-b border-gray-200 px-4 py-2">
                  <h2 className="text-sm leading-7 font-semibold text-gray-900">
                    Projects
                  </h2>
                  <Button href="/projects" variant="ghost" size="xs">
                    View all
                  </Button>
                </div>
                <div className="">
                  {isEmpty(projects?.data) && (
                    <div className="p-6">
                      <p className="text-gray-600">No projects yet.</p>
                    </div>
                  )}
                  {!isEmpty(projects?.data) && (
                    <ul role="list" className="divide-y divide-gray-200 px-6">
                      {projects?.data?.map((project) => (
                        <li
                          className="flex items-center justify-between py-4"
                          key={project.id}
                        >
                          <div className="min-w-0">
                            <div className="flex items-center gap-x-3">
                              <p className="text-sm leading-6 font-medium text-gray-900">
                                {project.name}
                              </p>
                              {/* <ProjectStatusBadge
                                status={project.status as ProjectStatus}
                              /> */}
                            </div>
                          </div>
                          <div className="flex flex-none items-center gap-x-4">
                            {/* <ProjectActionsMenu id={project.id} /> */}
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </Card>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="space-y-2">
            <Card>
              <div className="flex items-center justify-between border-b border-gray-200 px-4 py-2">
                <h2 className="text-sm leading-7 font-semibold text-gray-900">
                  Contact details
                </h2>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7"
                  onClick={editDetailsModeHandler.open}
                >
                  <IconPencil size={16} />
                </Button>
              </div>
              <div className="space-y-2 p-6">
                <div className="space-y-2">
                  <p className="flex items-center gap-x-3 text-gray-600">
                    <IconUser size={18} /> {getFullName(contact?.data)}
                  </p>
                  <p className="flex items-center gap-x-3 text-gray-600">
                    <IconMail size={18} /> {contact?.data?.email}
                  </p>
                  <p className="flex items-center gap-x-3 text-gray-600">
                    <IconPhone size={18} />{" "}
                    {formatPhoneNumber(contact?.data?.phone) || "-"}
                  </p>
                  <p className="flex items-center gap-x-3 text-gray-600">
                    <IconMapPin size={18} /> {contact?.data?.address || "-"}
                  </p>
                  <p className="flex items-center gap-x-3 text-gray-600">
                    <IconBuilding size={18} /> {contact?.data?.company || "-"}
                  </p>
                  <p className="flex items-center gap-x-3 text-gray-600">
                    <IconWorld size={18} />{" "}
                    {contact?.data?.website ? (
                      <Link
                        href={contact?.data?.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="underline-offset-4 hover:underline"
                      >
                        {contact?.data?.website}
                      </Link>
                    ) : (
                      "-"
                    )}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          <div className="space-y-2">
            <Card>
              <div className="flex items-center justify-between border-b border-gray-200 px-4 py-2">
                <h2 className="text-sm leading-7 font-semibold text-gray-900">
                  Recent documents
                </h2>
                <Button variant="ghost" size="icon" className="h-7 w-7">
                  <IconPlus size={16} />
                </Button>
              </div>
              <div className="space-y-2 p-6">
                <Button
                  className="w-full justify-start border-dashed py-6 text-base font-normal"
                  variant="outline"
                  leftIcon={<IconFileText size={22} />}
                >
                  Create proposal
                </Button>
                <Button
                  className="w-full justify-start border-dashed py-6 text-base font-normal"
                  variant="outline"
                  leftIcon={<IconReceiptDollar size={22} />}
                >
                  Create invoice
                </Button>
              </div>
            </Card>
          </div>

          <div className="space-y-2">
            <Card>
              <div className="flex items-center justify-between border-b border-gray-200 px-4 py-2">
                <h2 className="text-sm leading-7 font-semibold text-gray-900">
                  Notes
                </h2>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7"
                  onClick={() => setNotesEditMode(true)}
                >
                  <IconPencil size={16} />
                </Button>
              </div>
              <div className="space-y-2 p-6">
                {notesEditMode && (
                  <div className="space-y-4">
                    <TextareaAutosize
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="block w-full rounded-lg border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-primary focus:ring-inset sm:text-sm sm:leading-6"
                      placeholder="Add a note (this is only visible to you)..."
                      minRows={4}
                    />
                    <div className="space-x-2">
                      <Button
                        onClick={handleNotesUpdate}
                        loading={updateMutation.isPending}
                      >
                        Save changes
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setNotesEditMode(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
                {!notesEditMode && !notes && (
                  <p className="text-gray-600">No notes yet.</p>
                )}
                {!notesEditMode && notes && (
                  <p className="text-gray-600">{notes}</p>
                )}
              </div>
            </Card>
          </div>
        </div>
      </div>

      {contact?.data && (
        <ContactEditDialog
          open={editDetailsMode}
          onClose={editDetailsModeHandler.close}
          contact={contact.data}
        />
      )}
    </div>
  );
}
