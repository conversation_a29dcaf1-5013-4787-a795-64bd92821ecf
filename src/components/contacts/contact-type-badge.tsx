import { Badge, type BadgeVariants } from "@/components/ui/badge";
import { type ContactType } from "@/types/contact.types";

const colors: Record<ContactType, { color: BadgeVariants }> = {
  lead: { color: "blue" },
  client: { color: "green" },
  other: { color: "gray" },
};

interface Props {
  type: ContactType;
}

export function ContactTypeBadge({ type }: Props) {
  return (
    <Badge variant={colors[type]?.color || "gray"} className="capitalize">
      {type}
    </Badge>
  );
}
