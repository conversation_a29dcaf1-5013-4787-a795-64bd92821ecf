"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDialog } from "@/hooks/use-dialog";
import { useContactDeleteMutation } from "@/queries/contact.queries";
import { IconArchive, IconDots, IconEye, IconTrash } from "@tabler/icons-react";
import Link from "next/link";

interface Props {
  id: string;
}

export function ContactActionsMenu({ id }: Props) {
  const [openDialog, openDialogHandlers] = useDialog();

  const deleteContactMutation = useContactDeleteMutation();

  async function onDelete() {
    await deleteContactMutation.mutateAsync({ id });
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="h-auto p-1.5 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
          >
            <IconDots size={18} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[180px]">
          <Link href={`/contacts/${id}`}>
            <DropdownMenuItem>
              <IconEye className="mr-2 h-4 w-4" />
              <span>View contact</span>
            </DropdownMenuItem>
          </Link>
          <DropdownMenuItem onClick={openDialogHandlers.open}>
            <IconArchive className="mr-2 h-4 w-4" />
            <span>Archive</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            className="!text-red-500 hover:!bg-red-500/5"
            onClick={openDialogHandlers.open}
          >
            <IconTrash className="mr-2 h-4 w-4" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DeleteDialog
        title="Contact"
        open={openDialog}
        onClose={openDialogHandlers.close}
        onDelete={onDelete}
        loading={deleteContactMutation.isPending}
      />
    </>
  );
}
