"use client";

import { ContactActionsMenu } from "@/components/contacts/contact-actions-menu";
import { ContactCreateDialog } from "@/components/contacts/contact-create-dialog";
import { ContactTypeBadge } from "@/components/contacts/contact-type-badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable, DataTableFacetedFilter } from "@/components/ui/data-table";
import { EmptyState } from "@/components/ui/empty-state";
import { Loader } from "@/components/ui/loader";
import {
  PageContent,
  PageTitle,
  PageWrapper,
} from "@/components/ui/page-structure";
import { SearchInput } from "@/components/ui/search-input";
import { useDebouncedState } from "@/hooks/use-debounced-state";
import { useDialog } from "@/hooks/use-dialog";
import {
  useContactDeleteManyMutation,
  useInfiniteContacts,
} from "@/queries/contact.queries";
import type {
  Contact,
  ContactsOutput,
  ContactType,
  InfiniteContactsData,
} from "@/types/contact.types";
import { formatDate } from "@/utils/format-date";
import { formatPhoneNumber } from "@/utils/format-phone-number";
import { getFullName } from "@/utils/get-full-name";
import { getInitials } from "@/utils/get-initials";
import { IconPlus, IconUsers, IconX } from "@tabler/icons-react";
import { type ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { isEmpty } from "radash";
import { useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";

export const formatContacts = (contacts: InfiniteContactsData | undefined) => {
  let data = [] as Contact[];
  if (contacts?.pages) {
    for (const page of contacts?.pages) {
      data = [...data, ...page.data];
    }
    return data.map((contact) => ({
      ...contact,
      id: contact.id,
      initials: getInitials(`${contact?.firstName} ${contact?.lastName}`),
      email: contact.email || "-",
      phone: formatPhoneNumber(contact.phone) || "-",
      company: contact.company || "-",
      website: contact.website || "-",
    }));
  }
  return [];
};

export function ContactsView() {
  const { ref, inView } = useInView();
  const [openCreateModal, openCreateModalHandlers] = useDialog();
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [contactTypes, setContactTypes] = useState<Set<string>>(new Set());
  const [searchString, setSearchString] = useDebouncedState("", 250);

  const contacts = useInfiniteContacts({ searchString });

  useEffect(() => {
    if (contacts.hasNextPage && inView) {
      contacts.fetchNextPage();
    }
  }, [inView, contacts]);

  const deleteManyMutation = useContactDeleteManyMutation();

  async function deleteManyContacts() {
    setSelectedIds([]);
    await deleteManyMutation.mutateAsync({ ids: selectedIds });
  }

  const data = useMemo(() => formatContacts(contacts.data), [contacts]);

  const noSearchResults = isEmpty(data) && !isEmpty(searchString);

  const columns = useMemo<ColumnDef<Contact>[]>(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
            className="translate-y-[2px]"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "firstName",
        header: "Name",
        cell: (info) => (
          <Link href={`/contacts/${info?.row.original.id}`}>
            <div className="flex items-center space-x-3 hover:underline hover:underline-offset-4">
              <span className="font-medium">
                {getFullName(info?.row.original)}
              </span>
            </div>
          </Link>
        ),
      },
      {
        accessorKey: "email",
        header: () => "Email",
      },
      {
        accessorKey: "phone",
        header: () => "Phone",
      },
      {
        accessorKey: "company",
        header: "Company",
      },
      {
        accessorKey: "type",
        header: "Type",
        cell: (info) => (
          <ContactTypeBadge type={info.row.original.type as ContactType} />
        ),
      },
      {
        accessorKey: "createdAt",
        header: "Created",
        cell: (info) => <div>{formatDate(info.row.original.createdAt)}</div>,
      },
      {
        id: "actions",
        cell: (info) => (
          <div className="text-right">
            <ContactActionsMenu id={info?.row.original.id} />
          </div>
        ),
      },
    ],
    [],
  );

  return (
    <PageWrapper>
      <div className="flex items-center justify-between">
        <PageTitle>Contacts</PageTitle>
        <div>
          <Button
            leftIcon={<IconPlus size={16} />}
            onClick={openCreateModalHandlers.open}
          >
            New contact
          </Button>
        </div>
      </div>

      <div className="mt-4 flex space-x-3">
        <SearchInput
          placeholder="Search contacts"
          defaultValue={searchString}
          onChange={(event) => setSearchString(event.currentTarget.value)}
          disabled={selectedIds.length > 0}
        />
        <DataTableFacetedFilter
          options={[
            { label: "Lead", value: "lead" },
            { label: "Client", value: "client" },
            { label: "Other", value: "other" },
          ]}
          title="Type"
          setValues={setContactTypes}
          values={Array.from(contactTypes)}
        />
        {!isEmpty(Array.from(contactTypes)) && (
          <Button
            variant="ghost"
            onClick={() => setContactTypes(new Set([]))}
            rightIcon={<IconX size={16} />}
          >
            Reset
          </Button>
        )}
      </div>

      <PageContent>
        <DataTable
          data={data}
          columns={columns}
          setSelectedIds={setSelectedIds}
          selectedIds={selectedIds}
          onDelete={deleteManyContacts}
          isLoading={deleteManyMutation.isPending || contacts.isLoading}
          deleteModalTitle="Contact"
          emptyState={
            <>
              {!noSearchResults && (
                <EmptyState
                  title="No contacts yet"
                  subtitle="Get started by creating a new contact."
                  icon={<IconUsers size={40} />}
                  actionButton={
                    <Button
                      leftIcon={<IconPlus size={16} />}
                      onClick={openCreateModalHandlers.open}
                    >
                      New contact
                    </Button>
                  }
                />
              )}

              {noSearchResults && (
                <EmptyState
                  title="No search results"
                  subtitle="Please check the spelling or filter criteria"
                  icon={<IconUsers size={40} />}
                />
              )}
            </>
          }
        />
      </PageContent>

      <div ref={ref} className="text-center">
        {contacts.isFetchingNextPage && <Loader className="mt-5" />}
      </div>

      <ContactCreateDialog
        open={openCreateModal}
        onClose={openCreateModalHandlers.close}
      />
    </PageWrapper>
  );
}
