import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useContactAddMutation } from "@/queries/contact.queries";
import { contactCreateSchema } from "@/schemas/contacts.schemas";
import { type ContactCreateInput } from "@/types/contact.types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import TextareaAutosize from "react-textarea-autosize";

interface Props extends DialogProps {
  onClose: () => void;
}

export function ContactCreateDialog({ open, onClose }: Props) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ContactCreateInput>({
    resolver: zod<PERSON>esolver(contactCreateSchema),
  });

  const closeModal = () => {
    reset();
    onClose();
  };

  const handleContactSubmit = useContactAddMutation();

  async function onSubmit(data: ContactCreateInput) {
    try {
      await handleContactSubmit.mutateAsync(data);
      closeModal();
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[650px]">
        <DialogHeader>
          <DialogTitle>Create a new contact</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-3">
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-x-3 gap-y-6">
              <Input
                label="First name"
                {...register("firstName")}
                allowAutoComplete={false}
                error={errors.firstName !== undefined}
                errorMessage={errors?.firstName?.message}
              />
              <Input
                label="Last name"
                {...register("lastName")}
                allowAutoComplete={false}
                error={errors.lastName !== undefined}
                errorMessage={errors?.lastName?.message}
              />
            </div>
            <div className="grid grid-cols-2 gap-x-3 gap-y-6">
              <Input
                label="Email"
                {...register("email")}
                allowAutoComplete={false}
                error={errors.email !== undefined}
                errorMessage={errors?.email?.message}
              />
              <Input
                label="Phone"
                {...register("phone")}
                allowAutoComplete={false}
                error={errors.phone !== undefined}
                errorMessage={errors?.phone?.message}
              />
              <div>
                <Label>Contact type</Label>
                <Controller
                  control={control}
                  name="type"
                  render={({ field: { onChange } }) => (
                    <Select onValueChange={onChange} defaultValue="lead">
                      <SelectTrigger className="mt-[5px]">
                        <SelectValue placeholder="Select a type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="lead">Lead</SelectItem>
                        <SelectItem value="client">Client</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>

            <Input
              label="Company"
              {...register("company")}
              allowAutoComplete={false}
              error={errors.company !== undefined}
              errorMessage={errors?.company?.message}
            />
            <Input
              label="Website"
              {...register("website")}
              allowAutoComplete={false}
              error={errors.website !== undefined}
              errorMessage={errors?.website?.message}
            />
            <Input
              label="Address"
              {...register("address")}
              allowAutoComplete={false}
              error={errors.address !== undefined}
              errorMessage={errors?.address?.message}
            />

            <div>
              <Label>Notes</Label>
              <TextareaAutosize
                {...register("notes")}
                className="mt-[5px] block w-full rounded-lg border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-primary focus:ring-inset sm:text-sm sm:leading-6"
                minRows={4}
              />
            </div>
          </div>

          <DialogFooter className="mt-7">
            <Button variant="outline" onClick={closeModal} type="button">
              Close
            </Button>
            <Button loading={isSubmitting} type="submit">
              Create contact
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
