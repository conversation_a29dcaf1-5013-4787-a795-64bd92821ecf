import { cn } from "@/lib/utils";
import * as React from "react";

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  id?: string;
  label?: string;
  description?: string;
  icon?: React.ReactNode;
  error?: boolean;
  errorMessage?: string;
  classNames?: {
    label: string;
  };
  styles?: {
    label: React.CSSProperties;
  };
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      classNames,
      styles,
      label,
      description,
      id,
      icon,
      error,
      errorMessage,
      ...props
    },
    ref,
  ) => {
    return (
      <div>
        {label && (
          <label
            htmlFor={id}
            className={cn(
              "block text-sm leading-6 font-medium",
              classNames?.label,
            )}
            style={styles?.label}
          >
            {label}
          </label>
        )}
        {description && (
          <p className="block text-sm text-gray-500">{description}</p>
        )}
        <div className={cn("relative", label && "mt-[5px]")}>
          {icon && (
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span className="text-gray-400">{icon}</span>
            </div>
          )}
          <textarea
            id={id}
            className={cn(
              "flex min-h-[60px] w-full rounded-lg border-0 bg-transparent px-3 py-2 text-base shadow-xs ring-1 ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-primary focus:ring-inset focus-visible:ring-primary focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-60 md:text-sm",
              error && "ring-red-500 focus:ring-red-500!",
              icon && "pl-9",
              className,
            )}
            ref={ref}
            {...props}
          />
          {error && <p className="mt-1 text-sm text-red-500">{errorMessage}</p>}
        </div>
      </div>
    );
  },
);
Textarea.displayName = "Textarea";

export { Textarea };
