"use client";
import { Button, type ButtonVariants } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { formatDate } from "@/utils/format-date";
import { CalendarIcon } from "lucide-react";
import * as React from "react";

export interface DatePickerProps {
  useReactHookForm?: boolean;
  id?: string;
  label?: string;
  description?: string;
  error?: boolean;
  errorMessage?: string;
  placeholder?: string;
  value?: Date | null;
  onChange?: (date: Date | undefined) => void;
  variant?: ButtonVariants;
  disabled?: boolean;
  className?: string;
  classNames?: {
    label?: string;
    button?: string;
    popover?: string;
  };
  styles?: {
    label?: React.CSSProperties;
  };
}

export function DatePicker({
  id,
  label,
  description,
  error,
  errorMessage,
  placeholder = "Select date",
  value,
  onChange,
  variant = "outline",
  disabled,
  className,
  classNames,
  styles,
  useReactHookForm = false,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false);
  // Internal state only for RHF mode
  const [internalDate, setInternalDate] = React.useState<Date | null>(
    useReactHookForm ? (value ?? null) : null,
  );

  // Only update internal state if RHF mode and value changes
  React.useEffect(() => {
    if (useReactHookForm) {
      setInternalDate(value ?? null);
    }
  }, [value, useReactHookForm]);

  const handleSelect = (date: Date | undefined) => {
    if (useReactHookForm) {
      setInternalDate(date ?? null);
      if (onChange) onChange(date);
    } else {
      if (onChange) onChange(date);
    }
    setOpen(false);
  };

  return (
    <div className={className}>
      {label && (
        <Label
          htmlFor={id}
          className={cn(classNames?.label)}
          style={styles?.label}
        >
          {label}
        </Label>
      )}
      {description && (
        <p className="block text-sm text-gray-500">{description}</p>
      )}
      <div className={cn("relative", label && "mt-[5px]")}>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant={variant}
              id={id}
              disabled={disabled}
              className={cn(
                "justify-between font-normal",
                error && "ring-red-500 focus:ring-red-500",
                classNames?.button,
              )}
              rightIcon={<CalendarIcon size={16} />}
            >
              {useReactHookForm
                ? internalDate
                  ? formatDate(internalDate)
                  : placeholder
                : value
                  ? formatDate(value)
                  : placeholder}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className={cn("w-auto overflow-hidden p-0", classNames?.popover)}
            align="start"
          >
            <Calendar
              mode="single"
              selected={
                useReactHookForm
                  ? (internalDate ?? undefined)
                  : (value ?? undefined)
              }
              captionLayout="dropdown"
              onSelect={handleSelect}
              disabled={disabled}
            />
          </PopoverContent>
        </Popover>

        {error && errorMessage && (
          <p className="mt-1 text-sm text-red-500">{errorMessage}</p>
        )}
      </div>
    </div>
  );
}
