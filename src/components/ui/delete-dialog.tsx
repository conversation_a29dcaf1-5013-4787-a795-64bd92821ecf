import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  type DialogProps,
  DialogTitle,
} from "@/components/ui/dialog";
import { DialogDescription } from "@radix-ui/react-dialog";

interface Props extends DialogProps {
  /** Called when the delete action is confirmed */
  onDelete: () => Promise<void>;
  /** Called when the dialog should close without deleting */
  onClose: () => void;
  /** Title of the item being deleted, e.g., "Project" */
  title?: string;
  /** Optional custom description or message in the dialog */
  description?: React.ReactNode;
  /** Label for the confirm button (default "Delete") */
  confirmLabel?: string;
  /** Label for the cancel button (default "Cancel") */
  cancelLabel?: string;
  /** Loading state for the confirm button */
  loading?: boolean;
}

export function DeleteDialog({
  onClose,
  open,
  title,
  description,
  confirmLabel = "Delete",
  cancelLabel = "Cancel",
  onDelete,
  loading,
}: Props) {
  const defaultTitle = title ?? "item";

  function closeModal() {
    onClose();
  }

  async function handleDelete() {
    try {
      await onDelete();
      closeModal();
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader className="space-y-1 text-center">
          <DialogTitle>Delete {defaultTitle}</DialogTitle>
        </DialogHeader>

        <DialogDescription asChild>
          <p className="text-muted-foreground">
            {description ??
              `Are you sure you want to delete this ${defaultTitle}?`}
          </p>
        </DialogDescription>

        <DialogFooter className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={(e) => {
              e.stopPropagation();
              closeModal();
            }}
          >
            {cancelLabel}
          </Button>
          <Button
            variant="destructive"
            onClick={(e) => {
              e.stopPropagation();
              handleDelete();
            }}
            loading={loading}
          >
            {confirmLabel}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
