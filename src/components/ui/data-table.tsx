"use client";

import { <PERSON><PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useDialog } from "@/hooks/use-dialog";
import { cn } from "@/lib/utils";
import { IconCheck, IconCirclePlus, IconTrash } from "@tabler/icons-react";
import {
  type Column,
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import * as React from "react";
import { Affix } from "./affix";
import { Loader } from "./loader";

// import { DataTablePagination } from "../components/data-table-pagination";
// import { DataTableToolbar } from "../components/data-table-toolbar";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  setSelectedIds: (ids: string[]) => void;
  selectedIds: string[];
  onDelete: () => Promise<void>;
  isLoading?: boolean;
  isPending?: boolean;
  deleteModalTitle?: string;
  emptyState?: React.ReactNode;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  setSelectedIds,
  selectedIds,
  onDelete,
  isLoading,
  isPending,
  deleteModalTitle,
  emptyState,
}: DataTableProps<TData, TValue>) {
  const [openDeleteModal, openDeleteModalHandlers] = useDialog();
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  const selectedRowData = table
    .getSelectedRowModel()
    .flatRows.map((row: any) => row.original.id as string);

  React.useEffect(() => {
    setSelectedIds(selectedRowData);
  }, [rowSelection]);

  async function deleteData() {
    await onDelete();
    setRowSelection({});
  }

  function closeActions() {
    setSelectedIds([]);
    setRowSelection({});
  }

  return (
    <div className="space-y-4">
      {/* <DataTableToolbar table={table} /> */}
      <div className="overflow-hidden rounded-xl border border-gray-200">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      colSpan={header.colSpan}
                      className=""
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length && !isLoading ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className=""
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {isLoading && (
                    <div className="flex flex-col items-center py-20">
                      <Loader />
                    </div>
                  )}

                  {!isLoading && (
                    <>
                      {emptyState ? (
                        <div className="py-40">{emptyState}</div>
                      ) : (
                        "No results."
                      )}
                    </>
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {/* <DataTablePagination table={table} /> */}

      {selectedIds.length > 0 && (
        <Affix className="-bottom-3 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="flex w-[300px] items-center justify-between lg:w-[400px]">
            <div>
              <Button
                size="sm"
                leftIcon={<IconTrash size={16} />}
                variant="destructive"
                onClick={openDeleteModalHandlers.open}
              >
                Delete
              </Button>
            </div>
            <div className="flex items-center space-x-3">
              <p className="text-gray-500">
                {selectedIds.length} item{selectedIds.length > 1 && "s"}{" "}
                selected
              </p>
              <Button size="sm" variant="secondary" onClick={closeActions}>
                Cancel
              </Button>
            </div>
          </div>
        </Affix>
      )}

      <DeleteDialog
        title={deleteModalTitle}
        open={openDeleteModal}
        onClose={openDeleteModalHandlers.close}
        onDelete={deleteData}
        loading={isPending}
      />
    </div>
  );
}

const loadingData = new Array(5).fill("");

export function DataTableLoadingSkeleton() {
  return (
    <div className="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead
              scope="col"
              className="py-3.5 pr-3 pl-4 text-left text-sm font-semibold text-gray-900"
            >
              <Skeleton className="h-4 w-4" />
            </TableHead>
            <TableHead
              scope="col"
              className="hidden px-3 py-3.5 text-left text-sm font-semibold text-gray-900 sm:table-cell"
            >
              <Skeleton className="h-4 w-[100px]" />
            </TableHead>
            <TableHead
              scope="col"
              className="hidden px-3 py-3.5 text-left text-sm font-semibold text-gray-900 lg:table-cell"
            >
              <Skeleton className="h-4 w-[100px]" />
            </TableHead>
            <TableHead
              scope="col"
              className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
            >
              <Skeleton className="h-4 w-[100px]" />
            </TableHead>
            <TableHead
              scope="col"
              className="relative py-3.5 pr-4 pl-3 sm:pr-0"
            >
              <Skeleton className="h-4 w-4" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loadingData.map((_, index) => (
            <TableRow key={index}>
              <TableCell className="py-4 pr-3 pl-4 text-sm font-medium whitespace-nowrap text-gray-900">
                <Skeleton className="h-4 w-4" />
              </TableCell>
              <TableCell className="hidden px-3 py-4 text-sm whitespace-nowrap text-gray-500 sm:table-cell">
                <Skeleton className="h-4 w-[100px]" />
              </TableCell>
              <TableCell className="hidden px-3 py-4 text-sm whitespace-nowrap text-gray-500 lg:table-cell">
                <Skeleton className="h-4 w-[100px]" />
              </TableCell>
              <TableCell className="px-3 py-4 text-sm whitespace-nowrap text-gray-500">
                <Skeleton className="h-4 w-[100px]" />
              </TableCell>
              <TableCell className="py-4 pr-4 pl-3 text-right text-sm font-medium whitespace-nowrap sm:pr-0">
                <Skeleton className="h-4 w-4" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

interface DataTableFacetedFilterProps<TData, TValue> {
  column?: Column<TData, TValue>;
  title?: string;
  options: {
    label: string;
    value: string;
    icon?: React.ComponentType<{ className?: string }>;
  }[];
  setValues: React.Dispatch<React.SetStateAction<Set<any>>>;
  values: string[];
  classNames?: {
    container?: string;
    content?: string;
  };
}

/**
 * DataTableFacetedFilter is a reusable component for filtering table data by multiple selectable options.
 *
 * @template TData - The type of the table data.
 * @template TValue - The type of the column value.
 *
 * @param {Object} props - The component props.
 * @param {Column<TData, TValue>} [props.column] - The TanStack Table column instance to filter.
 * @param {string} [props.title] - The filter title to display.
 * @param {Array} props.options - The filter options, each with a label, value, and optional icon.
 * @param {React.Dispatch<React.SetStateAction<Set<any>>>} props.setValues - Setter for the selected filter values.
 * @param {string[]} props.values - The currently selected filter values.
 *
 * @example
 * // Example usage inside a table toolbar:
 * <DataTableFacetedFilter
 *   column={table.getColumn('status')}
 *   title="Status"
 *   options={[
 *     { label: 'Active', value: 'active' },
 *     { label: 'Inactive', value: 'inactive' },
 *   ]}
 *   setValues={setStatusFilter}
 *   values={Array.from(statusFilter)}
 * />
 *
 * @example
 * // With icons:
 * import { IconUser, IconX } from '@tabler/icons-react';
 * <DataTableFacetedFilter
 *   title="Role"
 *   options={[
 *     { label: 'User', value: 'user', icon: IconUser },
 *     { label: 'Admin', value: 'admin', icon: IconX },
 *   ]}
 *   setValues={setRoleFilter}
 *   values={Array.from(roleFilter)}
 * />
 */
export function DataTableFacetedFilter<TData, TValue>({
  column,
  title,
  options,
  setValues,
  values = [],
  classNames,
}: DataTableFacetedFilterProps<TData, TValue>) {
  const [selectedValues, setSelectedValues] = React.useState(new Set(values));
  const facets = column?.getFacetedUniqueValues();

  React.useEffect(() => {
    setSelectedValues(new Set(values));
  }, [values]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <DefaultButton variant="outline" className="border-dashed">
          <IconCirclePlus className="mr-2 h-4 w-4" />
          <div>{title}</div>
          {selectedValues?.size > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2.5 h-4" />
              <Badge
                variant="gray"
                className="rounded-sm font-normal lg:hidden"
              >
                {selectedValues.size}
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {selectedValues.size > 2 ? (
                  <Badge variant="gray" className="rounded-sm font-normal">
                    {selectedValues.size} selected
                  </Badge>
                ) : (
                  options
                    .filter((option) => selectedValues.has(option.value))
                    .map((option) => (
                      <Badge
                        variant="gray"
                        key={option.value}
                        className="rounded-sm font-normal"
                      >
                        {option.label}
                      </Badge>
                    ))
                )}
              </div>
            </>
          )}
        </DefaultButton>
      </PopoverTrigger>
      <PopoverContent
        className={cn("w-[200px] p-0", classNames?.content)}
        align="start"
      >
        <Command>
          <CommandInput
            placeholder={title}
            className="border-none focus:ring-0"
          />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => {
                const isSelected = selectedValues.has(option.value);
                return (
                  <CommandItem
                    className="cursor-pointer"
                    key={option.value}
                    onSelect={() => {
                      if (isSelected) {
                        selectedValues.delete(option.value);
                      } else {
                        selectedValues.add(option.value);
                      }
                      const filterValues = Array.from(selectedValues);
                      setSelectedValues(
                        filterValues.length
                          ? new Set(filterValues)
                          : new Set([] as string[]),
                      );
                      setValues(
                        filterValues.length
                          ? new Set(filterValues)
                          : new Set([] as string[]),
                      );
                    }}
                  >
                    <div
                      className={cn(
                        "mr-2 flex h-4.5 w-4.5 items-center justify-center rounded-[5px] border border-gray-400",
                        isSelected
                          ? "border-primary bg-primary text-primary-foreground"
                          : "opacity-50 [&_svg]:invisible",
                      )}
                    >
                      <IconCheck className={cn("h-3.5 w-3.5")} />
                    </div>
                    {option.icon && (
                      <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                    )}
                    <span>{option.label}</span>
                    {facets?.get(option.value) && (
                      <span className="ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs">
                        {facets.get(option.value)}
                      </span>
                    )}
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {selectedValues.size > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setSelectedValues(new Set([] as string[]));
                      setValues(new Set([] as string[]));
                    }}
                    className="cursor-pointer justify-center text-center"
                  >
                    Clear filters
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
