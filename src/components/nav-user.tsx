"use client";

import { I<PERSON><PERSON><PERSON><PERSON>, <PERSON>conSelector, IconUserCircle } from "@tabler/icons-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";
import { authClient } from "@/lib/auth-client";
import { APP_ROUTES } from "@/lib/constants";
import { useUser } from "@/queries/user.queries";
import { getInitials } from "@/utils/get-initials";
import { IconSettings } from "@tabler/icons-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export function NavUser() {
  const router = useRouter();
  const user = useUser();

  if (!user?.data) {
    return <Skeleton className="h-12 w-full bg-sidebar-accent" />;
  }

  const handleLogout = async () => {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          router.push(APP_ROUTES.LOGIN);
        },
      },
    });
  };

  const isAdmin = user.data.role === "admin";

  // Navigation items array for dropdown
  const userNavItems = [
    {
      href: "/settings",
      label: "Settings",
      icon: <IconSettings />,
      show: true,
      onClick: undefined,
    },
    {
      href: "/admin",
      label: "Admin",
      icon: <IconUserCircle />,
      show: isAdmin,
      onClick: undefined,
    },
    {
      href: undefined,
      label: "Log out",
      icon: <IconLogout />,
      show: true,
      onClick: handleLogout,
    },
  ];

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={user.data.image ?? ""} alt={user.data.name} />
                <AvatarFallback className="rounded-lg">
                  {getInitials(user.data.name)}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{user.data.name}</span>
                <span className="truncate text-xs">{user.data.email}</span>
              </div>
              <IconSelector className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg">
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage
                    src={user.data.image ?? ""}
                    alt={user.data.name}
                  />
                  <AvatarFallback className="rounded-lg">
                    {getInitials(user.data.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    {user.data.name}
                  </span>
                  <span className="truncate text-xs">{user.data.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {userNavItems
              .filter((item) => item.show)
              .map((item) =>
                item.href ? (
                  <DropdownMenuGroup key={item.label}>
                    <Link href={item.href}>
                      <DropdownMenuItem>
                        {item.icon}
                        {item.label}
                      </DropdownMenuItem>
                    </Link>
                  </DropdownMenuGroup>
                ) : (
                  <DropdownMenuItem key={item.label} onClick={item.onClick}>
                    {item.icon}
                    {item.label}
                  </DropdownMenuItem>
                ),
              )}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
