import { AppLogo } from "@/components/app-logo";
import { SidebarMenuButton } from "@/components/ui/sidebar";
import { APP_NAME } from "@/lib/constants";
import Link from "next/link";

interface NavHeaderProps {
  href?: string;
}

export function NavHeader({ href = "/dashboard" }: NavHeaderProps) {
  return (
    <SidebarMenuButton size="lg" asChild>
      <Link href={href}>
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg text-sidebar-primary-foreground">
          <AppLogo />
        </div>
        <div className="grid flex-1 text-left text-lg leading-tight">
          <span className="truncate font-semibold">{APP_NAME}</span>
        </div>
      </Link>
    </SidebarMenuButton>
  );
}
