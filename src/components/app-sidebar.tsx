"use client";

import * as React from "react";

import { ImpersonatedBy } from "@/components/impersonated-by";
import { NavHeader } from "@/components/nav-header";
import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { VerifyEmailMessage } from "@/components/verify-email-message";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <NavHeader />
      </SidebarHeader>
      <SidebarContent>
        <NavMain />
      </SidebarContent>
      <SidebarFooter>
        <VerifyEmailMessage />
        <ImpersonatedBy />
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
