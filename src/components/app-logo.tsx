// app/components/RelanceLogo.tsx

interface AppLogoProps {
  className?: string;
}

export const AppLogo = ({ className = "h-6 w-auto" }: AppLogoProps) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 177 227"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      xmlSpace="preserve"
      style={{
        fillRule: "evenodd",
        clipRule: "evenodd",
        strokeLinejoin: "round",
        strokeMiterlimit: 2,
      }}
      className={className}
    >
      <path
        d="M102.463,226.493l-73.7,-0l73.7,-111.448l73.702,-0l-73.702,111.448Z"
        style={{ fill: "#111928", fillRule: "nonzero" }}
      />
      <path
        d="M75.498,0l75.498,0l-75.498,115.045l-75.498,-0l75.498,-115.045Z"
        style={{ fill: "#111928", fillRule: "nonzero" }}
      />
    </svg>
  );
};
