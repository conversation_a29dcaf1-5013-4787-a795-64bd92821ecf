"use client";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  IconBriefcase,
  IconReceipt2,
  IconSettings,
  IconUsers,
} from "@tabler/icons-react";
import { HomeIcon } from "lucide-react";
import Link from "next/link";
import { usePathname, useSelectedLayoutSegment } from "next/navigation";

const menuItems = [
  {
    path: "dashboard",
    name: "Dashboard",
    icon: <HomeIcon size={50} />,
  },
  {
    path: "contacts",
    name: "Contacts",
    icon: <IconUsers size={50} />,
  },
  {
    path: "projects",
    name: "Projects",
    icon: <IconBriefcase size={50} />,
  },
  {
    path: "invoices",
    name: "Invoices",
    icon: <IconReceipt2 size={50} />,
  },
  {
    path: "settings",
    name: "Settings",
    icon: <IconSettings size={50} />,
  },
];

export function NavMain() {
  const segment = useSelectedLayoutSegment();
  const pathname = usePathname();

  function isActive({ path, name }: { path: string; name: string }) {
    return segment === name.toLowerCase() || pathname === path;
  }

  return (
    <SidebarGroup>
      <SidebarMenu>
        {menuItems.map((item) => (
          <Link key={item.path} href={`/${item.path}`}>
            <SidebarMenuItem>
              <SidebarMenuButton
                isActive={isActive({
                  path: item.path,
                  name: item.name,
                })}
                className="relative flex h-9 items-center gap-x-2 rounded-md border border-sidebar px-3 py-2 text-sm transition-all duration-200 ease-in-out data-[active=true]:border data-[active=true]:border-gray-200 data-[active=true]:bg-white data-[active=true]:shadow-xs"
              >
                {item.icon}
                <span>{item.name}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </Link>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
