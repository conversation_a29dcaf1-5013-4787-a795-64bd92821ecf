import { env } from "@/env";
import { FILTER_TAKE } from "@/lib/constants";
import { dayjs } from "@/lib/dayjs";
// import { sendInvoiceEmail } from "@/lib/mail";
import { filterSchema } from "@/schemas/api.schemas";
import {
  invoiceCreateSchema,
  invoiceUpdateSchema,
} from "@/schemas/invoice.schemas";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import type { InvoiceItem } from "@/types/invoice.types";
import { TRPCError } from "@trpc/server";
import { isEmpty, omit } from "radash";
import { z } from "zod";

export function padInvoiceNumber(
  invoiceNumber: string | undefined,
  update = false,
) {
  let numberString: string;
  if (!update) {
    const number = Number(invoiceNumber) + 1;
    numberString = "" + number;
  } else {
    numberString = "" + Number(invoiceNumber);
  }
  while (numberString.length < 6) {
    numberString = "0" + numberString;
  }
  return numberString;
}

export const invoiceRouter = createTRPCRouter({
  create: protectedProcedure
    .input(invoiceCreateSchema)
    .mutation(async ({ ctx, input }) => {
      const invoices = await ctx.db.invoice.findMany({
        where: { userId: ctx.session.user.id },
        orderBy: {
          id: "desc",
        },
        take: 1,
      });
      const lastInvoiceNumber = isEmpty(invoices)
        ? "0"
        : invoices[0]?.invoiceNumber;
      const invoiceNumber = padInvoiceNumber(lastInvoiceNumber);
      const issuedOn = new Date().toISOString();
      const dueOn = dayjs(issuedOn).add(1, "month").toISOString();

      return await ctx.db.invoice.create({
        data: {
          ...input,
          invoiceNumber,
          issuedOn,
          dueOn,
          userId: ctx.session.user.id,
        },
      });
    }),
  getAll: protectedProcedure
    .input(z.object({ status: z.string().optional(), ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const userQuery = { userId: ctx.session.user.id };

      const take = input?.take ? Number(input?.take) : FILTER_TAKE;

      const data = await ctx.db.invoice.findMany({
        where: {
          ...userQuery,
          archived: false,
          status: input?.status,
          OR: [
            {
              invoiceNumber: {
                contains: input?.searchString || "",
                mode: "insensitive",
              },
            },
            {
              toName: { contains: input?.searchString, mode: "insensitive" },
            },
          ],
        },
        select: {
          id: true,
          invoiceNumber: true,
          toName: true,
          toEmail: true,
          issuedOn: true,
          dueOn: true,
          total: true,
          status: true,
          paymentMethod: true,
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { id: "desc" },
      });

      const total = await ctx.db.invoice.count({ where: userQuery });

      const result = { total, data, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data?.at(-1)?.id };
    }),
  getById: protectedProcedure
    .input(z.object({ invoiceId: z.string() }))
    .query(async ({ ctx, input }) => {
      const invoice = await ctx.db.invoice.findUnique({
        where: { id: input.invoiceId },
        include: {
          contact: true,
        },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      return { ...invoice, items: invoice.items as InvoiceItem[] };
    }),
  getByIdPublic: publicProcedure
    .input(z.object({ invoiceId: z.string() }))
    .query(async ({ ctx, input }) => {
      const invoice = await ctx.db.invoice.findUnique({
        where: { id: input.invoiceId },
        include: {
          contact: true,
          user: true,
        },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      return { ...invoice, items: invoice.items as InvoiceItem[] };
    }),
  updateById: protectedProcedure
    .input(
      z
        .object({
          invoiceId: z.string(),
        })
        .merge(invoiceUpdateSchema),
    )
    .mutation(async ({ ctx, input }) => {
      if (input.invoiceNumber) {
        input.invoiceNumber = padInvoiceNumber(input.invoiceNumber, true);
      }

      return await ctx.db.invoice.update({
        where: { id: input.invoiceId },
        data: omit(input, ["invoiceId"]),
      });
    }),
  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input: { id } }) => {
      return await ctx.db.invoice.delete({ where: { id } });
    }),
  deleteMany: protectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ ctx, input: { ids } }) => {
      return await ctx.db.invoice.deleteMany({ where: { id: { in: ids } } });
    }),

  sendInvoiceEmail: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        messageHTML: z.string(),
        subject: z.string(),
        additionalRecipients: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const invoice = await ctx.db.invoice.findUnique({
        where: { id: input.id },
        include: {
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      const link = `${env.APP_BASE_URL}/invoices/${invoice.id}/pay`;
      // await sendInvoiceEmail({
      //   toEmail: invoice.toEmail,
      //   fromName: invoice.fromName,
      //   subject: input.subject,
      //   messageHTML: input.messageHTML,
      //   invoiceNumber: invoice.invoiceNumber,
      //   total: invoice.total,
      //   dueOn: invoice.dueOn,
      //   link,
      //   additionalRecipients: input.additionalRecipients,
      // });

      await ctx.db.invoice.update({
        where: { id: input.id },
        data: { sentDate: new Date().toISOString(), status: "sent" },
      });

      return { message: "sent" };
    }),
});
