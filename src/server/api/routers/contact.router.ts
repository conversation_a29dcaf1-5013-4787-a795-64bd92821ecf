import { FILTER_TAKE } from "@/lib/constants";
import { filterSchema } from "@/schemas/api.schemas";
import {
  contactCreateSchema,
  contactUpdateSchema,
} from "@/schemas/contacts.schemas";
import { TRPCError } from "@trpc/server";
import { omit } from "radash";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const contactRouter = createTRPCRouter({
  create: protectedProcedure
    .input(contactCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.contact.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
        },
      });
    }),
  getAll: protectedProcedure
    .input(z.object({ ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const userQuery = { userId: ctx.session.user.id };

      const take = input?.take ? Number(input?.take) : FILTER_TAKE;

      const data = await ctx.db.contact.findMany({
        where: {
          ...userQuery,
          archived: false,
          OR: [
            {
              firstName: {
                contains: input?.searchString || "",
                mode: "insensitive",
              },
            },
            {
              lastName: {
                contains: input?.searchString || "",
                mode: "insensitive",
              },
            },
            {
              email: {
                contains: input?.searchString || "",
                mode: "insensitive",
              },
            },
            {
              phone: {
                contains: input?.searchString || "",
                mode: "insensitive",
              },
            },
            {
              company: {
                contains: input?.searchString || "",
                mode: "insensitive",
              },
            },
          ],
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { id: "desc" },
      });

      const total = await ctx.db.contact.count({ where: userQuery });

      const result = { total, data, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data?.at(-1)?.id };
    }),
  getById: protectedProcedure
    .input(z.object({ contactId: z.string() }))
    .query(async ({ ctx, input }) => {
      const contact = await ctx.db.contact.findUnique({
        where: { id: input.contactId },
      });

      if (!contact) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Contact not found",
        });
      }

      return contact;
    }),
  updateById: protectedProcedure
    .input(
      z
        .object({
          contactId: z.string(),
        })
        .merge(contactUpdateSchema),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.contact.update({
        where: { id: input.contactId },
        data: omit(input, ["contactId"]),
      });
    }),
  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input: { id } }) => {
      return await ctx.db.contact.delete({ where: { id } });
    }),
  deleteMany: protectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ ctx, input: { ids } }) => {
      return await ctx.db.contact.deleteMany({ where: { id: { in: ids } } });
    }),
});
