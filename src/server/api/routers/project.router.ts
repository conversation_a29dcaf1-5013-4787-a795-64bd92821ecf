import { FILTER_TAKE } from "@/lib/constants";
import { filterSchema } from "@/schemas/api.schemas";
import {
  projectCreateSchema,
  projectUpdateSchema,
} from "@/schemas/project.schemas";
import { TRPCError } from "@trpc/server";
import { omit } from "radash";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const projectRouter = createTRPCRouter({
  create: protectedProcedure
    .input(projectCreateSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.project.create({
        data: {
          ...input,
          userId: ctx.session.user.id,
        },
      });
    }),
  getAll: protectedProcedure
    .input(z.object({ ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const userQuery = { userId: ctx.session.user.id };

      const take = input?.take ? Number(input?.take) : FILTER_TAKE;

      const data = await ctx.db.project.findMany({
        where: {
          ...userQuery,
          archived: false,
          OR: [
            {
              name: {
                contains: input?.searchString || "",
                mode: "insensitive",
              },
            },
          ],
        },
        include: {
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { id: "desc" },
      });

      const total = await ctx.db.project.count({ where: userQuery });

      const result = { total, data, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data?.at(-1)?.id };
    }),
  getById: protectedProcedure
    .input(z.object({ projectId: z.string() }))
    .query(async ({ ctx, input }) => {
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: {
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      return project;
    }),
  updateById: protectedProcedure
    .input(
      z
        .object({
          projectId: z.string(),
        })
        .merge(projectUpdateSchema),
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.project.update({
        where: { id: input.projectId },
        data: omit(input, ["projectId"]),
      });
    }),
  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input: { id } }) => {
      return await ctx.db.project.delete({ where: { id } });
    }),
  deleteMany: protectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ ctx, input: { ids } }) => {
      return await ctx.db.project.deleteMany({ where: { id: { in: ids } } });
    }),

  getProjectsByContactId: protectedProcedure
    .input(z.object({ contactId: z.string(), ...filterSchema }))
    .query(async ({ ctx, input }) => {
      const query = { contactId: input.contactId };

      const take = input?.take ? Number(input?.take) : FILTER_TAKE;

      const data = await ctx.db.project.findMany({
        where: {
          ...query,
          archived: false,
          OR: [
            {
              name: {
                contains: input?.searchString || "",
                mode: "insensitive",
              },
            },
          ],
        },
        include: {
          contact: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        ...(input.cursor && {
          cursor: {
            id: input.cursor,
          },
          skip: 1,
        }),
        take,
        orderBy: { id: "desc" },
      });

      const total = await ctx.db.project.count({ where: query });

      const result = { total, data, cursor: "" };

      if (data.length < take) return result;

      return { ...result, cursor: data?.at(-1)?.id };
    }),
});
