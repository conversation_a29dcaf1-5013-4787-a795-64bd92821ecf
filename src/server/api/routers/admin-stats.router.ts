import { createTRPCRouter, protectedProcedure } from "../trpc";

export const adminStatsRouter = createTRPCRouter({
  getUserStats: protectedProcedure.query(async ({ ctx }) => {
    const now = new Date();
    const ONE_DAY_AGO = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const SEVEN_DAYS_AGO = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const THIRTY_DAYS_AGO = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Basic user counts
    const totalUsers = await ctx.db.user.count();

    // Active users by time period
    const dailyActiveUsers = await ctx.db.user.count({
      where: {
        sessions: {
          some: {
            expiresAt: {
              gt: ONE_DAY_AGO,
            },
          },
        },
      },
    });

    const weeklyActiveUsers = await ctx.db.user.count({
      where: {
        sessions: {
          some: {
            expiresAt: {
              gt: SEVEN_DAYS_AGO,
            },
          },
        },
      },
    });

    const activeUsers = await ctx.db.user.count({
      where: {
        sessions: {
          some: {
            expiresAt: {
              gt: THIRTY_DAYS_AGO,
            },
          },
        },
      },
    });

    // New users this month: created in last 30 days
    const newUsersThisMonth = await ctx.db.user.count({
      where: {
        createdAt: {
          gt: THIRTY_DAYS_AGO,
        },
      },
    });

    // Email verification status
    const verifiedUsers = await ctx.db.user.count({
      where: {
        emailVerified: true,
      },
    });

    const unverifiedUsers = await ctx.db.user.count({
      where: {
        emailVerified: false,
      },
    });

    // Security features
    const passkeyUsers = await ctx.db.user.count({
      where: {
        passkeys: {
          some: {},
        },
      },
    });

    // Moderation metrics
    const bannedUsers = await ctx.db.user.count({
      where: {
        banned: true,
      },
    });

    // Users at risk (inactive for 7-14 days)
    const FOURTEEN_DAYS_AGO = new Date(
      now.getTime() - 14 * 24 * 60 * 60 * 1000,
    );
    const atRiskUsers = await ctx.db.user.count({
      where: {
        AND: [
          {
            sessions: {
              some: {
                expiresAt: {
                  lt: SEVEN_DAYS_AGO,
                },
              },
            },
          },
          {
            sessions: {
              some: {
                expiresAt: {
                  gt: FOURTEEN_DAYS_AGO,
                },
              },
            },
          },
        ],
      },
    });

    return {
      totalUsers,
      dailyActiveUsers,
      weeklyActiveUsers,
      activeUsers,
      newUsersThisMonth,
      verifiedUsers,
      unverifiedUsers,
      passkeyUsers,
      bannedUsers,
      atRiskUsers,
    };
  }),
});
