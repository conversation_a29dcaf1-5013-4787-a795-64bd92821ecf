import { api } from "@/trpc/react";
import type { ContactsFindInput } from "@/types/contact.types";
import { isEmpty } from "radash";
import { toast } from "sonner";

export const useContactById = (id: string) => {
  return api.contacts.getById.useQuery(
    { contactId: id },
    { enabled: !isEmpty(id) },
  );
};

export function useContacts() {
  return api.contacts.getAll.useQuery({});
}

export function useInfiniteContacts(input?: ContactsFindInput) {
  return api.contacts.getAll.useInfiniteQuery(
    { ...input },
    {
      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
}

export function useContactAddMutation() {
  const utils = api.useUtils();

  return api.contacts.create.useMutation({
    onMutate: async () => {
      await utils.contacts.getAll.cancel();
      const previousQueryData = utils.contacts.getAll.getInfiniteData();
      return { previousQueryData };
    },
    onError: (error, _input, ctx) => {
      console.log(error);
      utils.contacts.getAll.setInfiniteData({}, ctx?.previousQueryData);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.contacts.getAll.invalidate();
    },
  });
}

export const useContactUpdateMutation = () => {
  const utils = api.useUtils();

  return api.contacts.updateById.useMutation({
    onMutate: async (input) => {
      await utils.contacts.getById.cancel({ contactId: input.contactId });
      const previousQueryData = utils.contacts.getById.getData({
        contactId: input.contactId,
      });
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.contacts.getById.setData(
        { contactId: input.contactId },
        ctx?.previousQueryData,
      );
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.contacts.getById.invalidate({
        contactId: input.contactId,
      });
      // await utils.invoices.getById.invalidate();
    },
  });
};

export const useContactDeleteMutation = () => {
  const utils = api.useUtils();

  return api.contacts.deleteById.useMutation({
    onMutate: async (input) => {
      await utils.contacts.getById.cancel({ contactId: input.id });
      const previousQueryData = utils.contacts.getById.getData({
        contactId: input.id,
      });
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.contacts.getById.setData(
        { contactId: input.id },
        ctx?.previousQueryData,
      );
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.contacts.getById.invalidate({ contactId: input.id });
      await utils.contacts.getAll.invalidate();
    },
  });
};

export const useContactDeleteManyMutation = () => {
  const utils = api.useUtils();

  return api.contacts.deleteMany.useMutation({
    onMutate: async (_input) => {
      await utils.contacts.getAll.cancel();
      const previousQueryData = utils.contacts.getAll.getData();
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.contacts.getAll.setData({}, ctx?.previousQueryData);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.contacts.getAll.invalidate();
    },
  });
};
