import { api } from "@/trpc/react";
import { type InvoicesFindInput } from "@/types/invoice.types";
import { useRouter } from "next/navigation";
import { isEmpty } from "radash";
import { toast } from "sonner";

export const useInvoiceById = (
  id: string,
  { refetchOnWindowFocus = true } = {},
) => {
  return api.invoices.getById.useQuery(
    { invoiceId: id },
    { enabled: !isEmpty(id), refetchOnWindowFocus },
  );
};

export const useInvoiceByIdPublic = (
  id: string,
  { refetchOnWindowFocus = true } = {},
) => {
  return api.invoices.getByIdPublic.useQuery(
    { invoiceId: id },
    { enabled: !isEmpty(id), refetchOnWindowFocus },
  );
};

export function useInvoices() {
  return api.invoices.getAll.useQuery({});
}

export function useInfiniteInvoices(input?: InvoicesFindInput) {
  return api.invoices.getAll.useInfiniteQuery(
    { ...input },
    {
      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
}

export function useInvoiceAddMutation() {
  const router = useRouter();
  const utils = api.useUtils();

  return api.invoices.create.useMutation({
    onMutate: async () => {
      await utils.invoices.getAll.cancel();
      const previousQueryData = utils.invoices.getAll.getInfiniteData();
      return { previousQueryData };
    },
    onSuccess: (data) => {
      router.push(`/invoices/${data.id}/edit`);
    },
    onError: (error, _input, ctx) => {
      console.log(error);
      utils.invoices.getAll.setInfiniteData({}, ctx?.previousQueryData);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.invoices.getAll.invalidate();
    },
  });
}

export const useInvoiceUpdateMutation = () => {
  const utils = api.useUtils();

  return api.invoices.updateById.useMutation({
    onMutate: async (input) => {
      await utils.invoices.getById.cancel({ invoiceId: input.invoiceId });
      const previousQueryData = utils.invoices.getById.getData({
        invoiceId: input.invoiceId,
      });
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.invoices.getById.setData(
        { invoiceId: input.invoiceId },
        ctx?.previousQueryData,
      );
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.invoices.getById.invalidate({
        invoiceId: input.invoiceId,
      });
    },
  });
};

export const useInvoiceDeleteMutation = () => {
  const utils = api.useUtils();

  return api.invoices.deleteById.useMutation({
    onMutate: async (input) => {
      await utils.invoices.getById.cancel({ invoiceId: input.id });
      const previousQueryData = utils.invoices.getById.getData({
        invoiceId: input.id,
      });
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.invoices.getById.setData(
        { invoiceId: input.id },
        ctx?.previousQueryData,
      );
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.invoices.getById.invalidate({ invoiceId: input.id });
      await utils.invoices.getAll.invalidate();
    },
  });
};

export const useInvoiceDeleteManyMutation = () => {
  const utils = api.useUtils();

  return api.invoices.deleteMany.useMutation({
    onMutate: async (_input) => {
      await utils.invoices.getAll.cancel();
      const previousQueryData = utils.invoices.getAll.getData();
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.invoices.getAll.setData({}, ctx?.previousQueryData);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.invoices.getAll.invalidate();
    },
  });
};
