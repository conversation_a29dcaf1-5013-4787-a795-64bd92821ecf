import { api } from "@/trpc/react";
import {
  type ProjectsByContactIdFindInput,
  type ProjectsFindInput,
} from "@/types/project.types";
import { isEmpty } from "radash";
import { toast } from "sonner";

export const useProjectById = (id: string) => {
  return api.projects.getById.useQuery(
    { projectId: id },
    { enabled: !isEmpty(id) },
  );
};

export function useProjects() {
  return api.projects.getAll.useQuery({});
}

export function useProjectsByContactId(contactId: string, take = 5) {
  return api.projects.getProjectsByContactId.useQuery({ contactId, take });
}

export function useInfiniteProjects(input?: ProjectsFindInput) {
  return api.projects.getAll.useInfiniteQuery(
    { ...input },
    {
      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
}

export function useInfiniteProjectsByContactId(
  input?: ProjectsByContactIdFindInput,
) {
  return api.projects.getAll.useInfiniteQuery(
    { ...input },
    {
      getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
    },
  );
}

export function useProjectAddMutation() {
  const utils = api.useUtils();

  return api.projects.create.useMutation({
    onMutate: async () => {
      await utils.projects.getAll.cancel();
      const previousQueryData = utils.projects.getAll.getInfiniteData();
      return { previousQueryData };
    },
    onError: (error, _input, ctx) => {
      console.log(error);
      utils.projects.getAll.setInfiniteData({}, ctx?.previousQueryData);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.projects.getAll.invalidate();
    },
  });
}

export const useProjectUpdateMutation = () => {
  const utils = api.useUtils();

  return api.projects.updateById.useMutation({
    onMutate: async (input) => {
      await utils.projects.getById.cancel({ projectId: input.projectId });
      const previousQueryData = utils.projects.getById.getData({
        projectId: input.projectId,
      });
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.projects.getById.setData(
        { projectId: input.projectId },
        ctx?.previousQueryData,
      );
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.projects.getById.invalidate({
        projectId: input.projectId,
      });
      await utils.projects.getAll.invalidate();
    },
  });
};

export const useProjectDeleteMutation = () => {
  const utils = api.useUtils();

  return api.projects.deleteById.useMutation({
    onMutate: async (input) => {
      await utils.projects.getById.cancel({ projectId: input.id });
      const previousQueryData = utils.projects.getById.getData({
        projectId: input.id,
      });
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.projects.getById.setData(
        { projectId: input.id },
        ctx?.previousQueryData,
      );
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.projects.getById.invalidate({ projectId: input.id });
      await utils.projects.getProjectsByContactId.invalidate();
      await utils.projects.getAll.invalidate();
    },
  });
};

export const useProjectDeleteManyMutation = () => {
  const utils = api.useUtils();

  return api.projects.deleteMany.useMutation({
    onMutate: async (_input) => {
      await utils.projects.getAll.cancel();
      const previousQueryData = utils.projects.getAll.getData();
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.projects.getAll.setData({}, ctx?.previousQueryData);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.projects.getAll.invalidate();
    },
  });
};
