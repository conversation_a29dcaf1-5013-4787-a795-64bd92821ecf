---
applyTo: "**"
---

# Memory

### Button Component Patterns

The user's Button component follows specific patterns:

#### Icon Usage

- Use `leftIcon` and `rightIcon` props instead of placing icons as children
- Icons should be passed as JSX elements: `leftIcon={<IconPlus size={16} />}`
- Never manually add `gap-2` classes as this is handled internally

#### Size and Variants

- `size="sm"` provides appropriate button sizing for most UI elements
- `variant="ghost"` for subtle action buttons
- Remove manual height classes like `h-8` when using proper size props

#### Best Practices

- Always use the Button component's built-in props rather than manual styling
- Maintain consistency with the component's internal spacing and layout
- Use semantic props like `leftIcon`, `rightIcon`, `loading`, etc.

## Design Philosophy

- Prefer clean, minimal designs without crazy hover effects or gradients
- Focus on subtle interactions and good spacing
- Use modern layout patterns with proper visual hierarchy
- Maintain consistency with existing component patterns

## Card Component Guidelines

- **Never modify Card component border and shadow styles** unless explicitly told to do so
- Respect the existing Card component's default styling for borders and shadows
- Only update Card content, layout, and internal elements unless border/shadow changes are specifically requested
