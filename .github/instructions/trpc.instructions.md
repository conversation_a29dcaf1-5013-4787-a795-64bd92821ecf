---
applyTo: "**"
---

# tRPC Implementation Patterns

## Core Architecture

### Router Structure

- **Main Router**: `src/server/api/root.ts` - Central router that combines all feature routers
- **Feature Routers**: `src/server/api/routers/*.router.ts` - Individual routers for each domain (forms, users, etc.)
- **Base Setup**: `src/server/api/trpc.ts` - Core tRPC configuration with context, procedures, and middleware

### Procedure Types

```typescript
// Protected procedure - requires authentication
export const protectedProcedure = t.procedure
  .use(timingMiddleware)
  .use(({ ctx, next }) => {
    if (!ctx.session?.session || !ctx.session?.user) {
      throw new TRPCError({ code: "UNAUTHORIZED" });
    }
    return next({
      ctx: { session: { ...ctx.session, user: ctx.session.user } },
    });
  });

// Public procedure - no authentication required
export const publicProcedure = t.procedure.use(timingMiddleware);
```

## Router Implementation Patterns

### Basic Router Structure

```typescript
export const featureRouter = createTRPCRouter({
  // Query procedures
  getAll: protectedProcedure
    .input(
      z.object({
        /* input schema */
      }),
    )
    .query(async ({ ctx, input }) => {
      // Implementation
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      // Implementation with error handling
      const item = await ctx.db.model.findUnique({ where: { id: input.id } });
      if (!item) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Item not found" });
      }
      return item;
    }),

  // Mutation procedures
  create: protectedProcedure
    .input(createSchema)
    .mutation(async ({ ctx, input }) => {
      return ctx.db.model.create({ data: input });
    }),

  updateById: protectedProcedure
    .input(updateSchema)
    .mutation(async ({ ctx, input }) => {
      return ctx.db.model.update({
        where: { id: input.id },
        data: omit(input, ["id"]),
      });
    }),

  deleteById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return ctx.db.model.delete({ where: { id: input.id } });
    }),
});
```

### Input Validation Patterns

- **Dedicated Schema Files**: Store schemas in `src/schemas/feature.schemas.ts`
- **Schema Composition**: Use spread operator to combine common schemas like `filterSchema`
- **Inline Schemas**: Use `z.object()` directly for simple inputs

```typescript
// Schema composition example
.input(z.object({
  orgId: z.string(),
  type: z.string().optional(),
  ...filterSchema, // Spreads: searchString, cursor, sort, take
}))
```

### Pagination Pattern

```typescript
// Standard pagination with cursor
const data = await ctx.db.model.findMany({
  where: {
    /* filters */
  },
  ...(input.cursor && {
    cursor: { id: input.cursor },
    skip: 1,
  }),
  take: input.take ?? FILTER_TAKE,
  orderBy: { createdAt: "desc" },
});

// Return format
const result = { data, cursor: "" };
if (data.length < take) return result;
return { ...result, cursor: data.at(-1)?.id };
```

## Type Generation and Usage

### Router Type Inference

```typescript
// In src/trpc/react.tsx
export type RouterInputs = inferRouterInputs<AppRouter>;
export type RouterOutputs = inferRouterOutputs<AppRouter>;
```

### Type Definition Patterns

```typescript
// In feature type files (e.g., src/types/form.types.ts)
import { type RouterInputs, type RouterOutputs } from "@/trpc/react";

// Input types for mutations
export type FormCreateData = RouterInputs["form"]["create"];
export type FormUpdateData = RouterInputs["form"]["updateById"];

// Output types for queries
export type FormsOutput = RouterOutputs["form"]["getAll"];
export type FormOutput = RouterOutputs["form"]["getAll"]["data"][0]; // Single item from array
export type FormByIdOutput = RouterOutputs["form"]["getById"];

// Input types for queries
export type FormFindInput = RouterInputs["form"]["getAll"];

// Infinite query types
export type InfiniteFormsData = InfiniteData<FormsOutput> | undefined;
```

## Client Usage Patterns

### React Query Hooks

```typescript
// In src/queries/feature.queries.ts
export const useFeatureById = (id: string, orgId: string) => {
  return api.feature.getById.useQuery(
    { id, orgId },
    {
      enabled: !isEmpty(id) && !isEmpty(orgId),
      refetchOnWindowFocus: true,
    },
  );
};

export const useInfiniteFeatures = (input: FeatureFindInput) => {
  return api.feature.getAll.useInfiniteQuery(
    { ...input },
    {
      getNextPageParam: (lastPage) => lastPage.cursor || undefined,
    },
  );
};
```

### Mutation Patterns with Optimistic Updates

```typescript
export const useFeatureUpdateMutation = (orgId: string) => {
  const apiUtils = api.useUtils();

  return api.feature.updateById.useMutation({
    onMutate: async (input) => {
      await apiUtils.feature.getById.cancel({ id: input.id, orgId });
      const previousQueryData = apiUtils.feature.getById.getData({
        id: input.id,
        orgId,
      });
      return { previousQueryData };
    },
    onSuccess: () => {
      toast.success("Updated successfully");
    },
    onError: (error, input, ctx) => {
      apiUtils.feature.getById.setData(
        { id: input.id, orgId },
        ctx?.previousQueryData,
      );
      toast.error("Error", { description: error.message });
    },
    onSettled: async (data, error, input) => {
      await apiUtils.feature.getById.invalidate({ id: input.id, orgId });
      await apiUtils.feature.getAll.invalidate();
    },
  });
};
```

## Error Handling Patterns

### Server-side Error Handling

```typescript
// Standard not found pattern
const item = await ctx.db.model.findUnique({ where: { id: input.id } });
if (!item) {
  throw new TRPCError({
    code: "NOT_FOUND",
    message: "Item not found",
  });
}

// Authorization check pattern
if (item.organizationId !== input.orgId) {
  throw new TRPCError({
    code: "NOT_FOUND", // Use NOT_FOUND instead of FORBIDDEN for security
    message: "Item not found",
  });
}
```

### Client-side Error Handling

- Errors are automatically handled by React Query
- Use `onError` callbacks in mutations for custom error handling
- Toast notifications for user feedback

## Context Patterns

### Database Access

- Always use `ctx.db` for database operations
- Leverage Prisma's type safety and query building

### Session Access

- `ctx.session` available in all procedures
- `ctx.session.user` guaranteed non-null in `protectedProcedure`
- Use session data for authorization and audit trails

## Advanced Patterns

### Complex Data Transformations

```typescript
// Application-side computed fields
const updatedData = data.map((item) => {
  const computedField = someBusinessLogic(item);
  return { ...item, computedField };
});
```

### Cache Invalidation

```typescript
// Invalidate related caches after mutations
await redis.del(`cache_key:${input.id}`);
```

### Batch Operations

```typescript
// Use database transactions for related operations
const result = await ctx.db.$transaction([
  ctx.db.model1.create({ data: input1 }),
  ctx.db.model2.update({ where: { id }, data: input2 }),
]);
```

## File Organization

- **Routers**: `src/server/api/routers/feature.router.ts`
- **Schemas**: `src/schemas/feature.schemas.ts`
- **Types**: `src/types/feature.types.ts`
- **Queries**: `src/queries/feature.queries.ts`
- **Client Setup**: `src/trpc/react.tsx` and `src/trpc/server.ts`

## Best Practices

1. **Consistent Naming**: Use `getAll`, `getById`, `create`, `updateById`, `deleteById` for CRUD operations
2. **Input Validation**: Always validate inputs with Zod schemas
3. **Error Handling**: Use appropriate tRPC error codes and descriptive messages
4. **Type Safety**: Leverage RouterInputs and RouterOutputs for full type safety
5. **Cache Management**: Properly invalidate caches after mutations
6. **Authorization**: Check permissions in procedures, not just middleware
7. **Pagination**: Use cursor-based pagination for large datasets
