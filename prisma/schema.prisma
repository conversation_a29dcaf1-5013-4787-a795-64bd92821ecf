// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client"
  output          = "../src/lib/generated/prisma"
  previewFeatures = ["driverAdapters", "queryCompiler"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String    @id @default(cuid(2))
  name               String
  email              String
  emailVerified      Boolean
  image              String?
  role               String?
  banned             Boolean?
  banReason          String?
  banExpires         DateTime?
  businessName       String?   @default("")
  jobTitle           String?   @default("")
  logo               String?   @default("")
  website            String?   @default("")
  phone              String?   @default("")
  address            String?   @default("")
  currency           String?   @default("usd")
  timezone           String?   @default("")
  stripeCustomerId   String?
  stripeAccountId    String?   @unique
  isStripeConnected  Boolean   @default(false)
  isStripeUpToDate   Boolean   @default(false)
  isStripeEnabled    Boolean   @default(false)
  paypalAccountId    String?
  isPaypalConnected  Boolean   @default(false)
  isPaypalEnabled    Boolean   @default(false)
  cashappAccountId   String?
  isCashappConnected Boolean   @default(false)
  isCashappEnabled   Boolean   @default(false)
  zelleAccountId     String?
  isZelleConnected   Boolean   @default(false)
  isZelleEnabled     Boolean   @default(false)
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  sessions           Session[]
  accounts           Account[]
  passkeys           Passkey[]
  contacts           Contact[]
  projects           Project[]
  invoices           Invoice[]

  @@unique([email])
  @@map("users")
}

model Subscription {
  id                   String    @id @default(cuid(2))
  plan                 String
  referenceId          String
  stripeCustomerId     String?
  stripeSubscriptionId String?
  status               String
  periodStart          DateTime?
  periodEnd            DateTime?
  cancelAtPeriodEnd    Boolean?
  seats                Int?
  trialStart           DateTime?
  trialEnd             DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  @@index([stripeCustomerId])
  @@map("subscriptions")
}

model Session {
  id             String   @id @default(cuid(2))
  expiresAt      DateTime
  token          String
  ipAddress      String?
  userAgent      String?
  userId         String
  impersonatedBy String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@index([userId])
  @@map("sessions")
}

model Account {
  id                    String    @id @default(cuid(2))
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@index([userId])
  @@map("accounts")
}

model Verification {
  id         String   @id @default(cuid(2))
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("verifications")
}

model Passkey {
  id           String   @id @default(cuid(2))
  name         String?
  publicKey    String
  userId       String
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  credentialID String
  counter      Int
  deviceType   String
  backedUp     Boolean
  transports   String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@index([userId])
  @@map("passkeys")
}

model Contact {
  id        String    @id @default(cuid(2))
  address   String    @default("")
  company   String    @default("")
  email     String    @unique
  firstName String
  lastName  String
  phone     String    @default("")
  type      String    @default("")
  userId    String
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  website   String    @default("")
  notes     String    @default("")
  archived  Boolean   @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  projects  Project[]
  invoices  Invoice[]

  @@index([userId])
  @@map("contacts")
}

model Project {
  id          String    @id @default(cuid(2))
  name        String
  description String    @default("")
  endDate     String    @default("")
  status      String    @default("")
  notes       String    @default("")
  contact     Contact?  @relation(fields: [contactId], references: [id])
  contactId   String?
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  archived    Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  invoices    Invoice[]

  @@index([contactId])
  @@index([userId])
  @@map("projects")
}

model Invoice {
  id               String   @id @default(cuid(2))
  amountDue        Int      @default(0)
  amountPaid       Int      @default(0)
  discount         Float    @default(0)
  dueOn            String   @default("")
  fromAddress      String   @default("")
  fromCompany      String   @default("")
  fromName         String   @default("")
  fromEmail        String   @default("")
  invoiceNumber    String   @default("")
  issuedOn         String   @default("")
  items            Json     @default("[]")
  unitsType        String   @default("Qty")
  notes            String   @default("")
  paymentDate      String   @default("")
  logoImage        String?  @default("")
  fee              Int      @default(0)
  net              Int      @default(0)
  paymentMethod    String?
  transactionId    String?
  isStripeEnabled  Boolean  @default(false)
  isPaypalEnabled  Boolean  @default(false)
  isCashappEnabled Boolean  @default(false)
  isZelleEnabled   Boolean  @default(false)
  sentDate         String   @default("")
  status           String   @default("draft")
  subtotal         Float    @default(0)
  tax              Float    @default(0)
  taxLabel         String   @default("Tax")
  toAddress        String   @default("")
  toCompany        String   @default("")
  toEmail          String   @default("")
  toName           String   @default("")
  total            Int      @default(0)
  type             String   @default("one_time")
  archived         Boolean  @default(false)
  userId           String
  user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  contactId        String?
  contact          Contact? @relation(fields: [contactId], references: [id])
  projectId        String?
  project          Project? @relation(fields: [projectId], references: [id])
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@index([contactId])
  @@index([projectId])
  @@index([userId])
  @@map("invoices")
}
