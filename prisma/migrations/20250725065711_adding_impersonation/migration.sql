/*
  Warnings:

  - You are about to drop the column `address` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `businessName` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `cashappAccountId` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `currency` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isCashappConnected` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isCashappEnabled` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isPaypalConnected` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isPaypalEnabled` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isStripeConnected` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isStripeEnabled` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isStripeUpToDate` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isZelleConnected` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `isZelleEnabled` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `jobTitle` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `logo` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `paypalAccountId` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `phone` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `stripeAccountId` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `timezone` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `website` on the `users` table. All the data in the column will be lost.
  - You are about to drop the column `zelleAccountId` on the `users` table. All the data in the column will be lost.
  - You are about to drop the `account` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `contacts` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `invoices` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `projects` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "account" DROP CONSTRAINT "account_userId_fkey";

-- DropForeignKey
ALTER TABLE "contacts" DROP CONSTRAINT "contacts_userId_fkey";

-- DropForeignKey
ALTER TABLE "invoices" DROP CONSTRAINT "invoices_contactId_fkey";

-- DropForeignKey
ALTER TABLE "invoices" DROP CONSTRAINT "invoices_projectId_fkey";

-- DropForeignKey
ALTER TABLE "invoices" DROP CONSTRAINT "invoices_userId_fkey";

-- DropForeignKey
ALTER TABLE "projects" DROP CONSTRAINT "projects_contactId_fkey";

-- DropForeignKey
ALTER TABLE "projects" DROP CONSTRAINT "projects_userId_fkey";

-- DropIndex
DROP INDEX "users_stripeAccountId_key";

-- AlterTable
ALTER TABLE "users" DROP COLUMN "address",
DROP COLUMN "businessName",
DROP COLUMN "cashappAccountId",
DROP COLUMN "currency",
DROP COLUMN "isCashappConnected",
DROP COLUMN "isCashappEnabled",
DROP COLUMN "isPaypalConnected",
DROP COLUMN "isPaypalEnabled",
DROP COLUMN "isStripeConnected",
DROP COLUMN "isStripeEnabled",
DROP COLUMN "isStripeUpToDate",
DROP COLUMN "isZelleConnected",
DROP COLUMN "isZelleEnabled",
DROP COLUMN "jobTitle",
DROP COLUMN "logo",
DROP COLUMN "paypalAccountId",
DROP COLUMN "phone",
DROP COLUMN "stripeAccountId",
DROP COLUMN "timezone",
DROP COLUMN "website",
DROP COLUMN "zelleAccountId",
ADD COLUMN     "impersonatedBy" TEXT;

-- DropTable
DROP TABLE "account";

-- DropTable
DROP TABLE "contacts";

-- DropTable
DROP TABLE "invoices";

-- DropTable
DROP TABLE "projects";

-- CreateTable
CREATE TABLE "accounts" (
    "id" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "providerId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "idToken" TEXT,
    "accessTokenExpiresAt" TIMESTAMP(3),
    "refreshTokenExpiresAt" TIMESTAMP(3),
    "scope" TEXT,
    "password" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
